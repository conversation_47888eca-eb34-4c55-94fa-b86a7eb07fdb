package com.fastbee.common.utils;

import java.util.Collection;
import java.util.Map;

/**
 * 对象转换工具类
 */
public class oConvertUtils {
    
    /**
     * 判断对象是否为空
     * @param obj 对象
     * @return true-为空，false-不为空
     */
    public static boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        }
        if (obj instanceof String) {
            return ((String) obj).trim().isEmpty();
        }
        if (obj instanceof Collection) {
            return ((Collection<?>) obj).isEmpty();
        }
        if (obj instanceof Map) {
            return ((Map<?, ?>) obj).isEmpty();
        }
        if (obj.getClass().isArray()) {
            return ((Object[]) obj).length == 0;
        }
        return false;
    }
    
    /**
     * 判断对象是否不为空
     * @param obj 对象
     * @return true-不为空，false-为空
     */
    public static boolean isNotEmpty(Object obj) {
        return !isEmpty(obj);
    }
    
    /**
     * 字符串转换
     * @param obj 对象
     * @return 字符串
     */
    public static String getString(Object obj) {
        return obj == null ? "" : obj.toString().trim();
    }
    
    /**
     * 字符串转换（带默认值）
     * @param obj 对象
     * @param defaultValue 默认值
     * @return 字符串
     */
    public static String getString(Object obj, String defaultValue) {
        return isEmpty(obj) ? defaultValue : obj.toString().trim();
    }
}
