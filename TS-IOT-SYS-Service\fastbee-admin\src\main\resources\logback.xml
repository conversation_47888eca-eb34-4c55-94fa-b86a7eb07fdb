<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 日志存放路径 -->
    <property name="log.path" value="/logs" />
    <!-- 日志输出格式 -->
    <property name="log.pattern" value="%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - [%method,%line] - %msg%n" />

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!-- ERROR 日志文件 -->
    <appender name="file_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/sys-error.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/sys-error.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder><pattern>${log.pattern}</pattern></encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter"><level>ERROR</level></filter>
    </appender>

    <!-- 第三方库日志控制（可选保留） -->
    <logger name="io.lettuce.core" level="ERROR"/>
    <logger name="reactor.netty" level="ERROR"/>
    <logger name="org.apache.http" level="ERROR"/>
    <logger name="org.springframework" level="ERROR"/>
    <logger name="org.mybatis" level="ERROR"/>
    <logger name="com.baomidou.mybatisplus" level="ERROR"/>
    <logger name="io.netty" level="ERROR"/>
    <logger name="org.hibernate" level="ERROR"/>
    <logger name="org.quartz" level="ERROR"/>
    <logger name="org.apache.catalina.startup.DigesterFactory" level="ERROR"/>
    <logger name="org.apache.coyote.http11.Http11NioProtocol" level="ERROR"/>
    <logger name="org.apache.tomcat.util.net.NioSelectorPool" level="ERROR"/>

    <!-- 模块日志控制 -->
    <logger name="com.fastbee" level="ERROR" />

    <!-- 全局日志配置：只输出 ERROR -->
    <root level="error">
        <appender-ref ref="console" />
        <appender-ref ref="file_error" />
    </root>
</configuration>