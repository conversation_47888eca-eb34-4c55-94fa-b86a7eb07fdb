package com.fastbee.device.mapper;

import com.fastbee.device.domain.DeviceMonitor.*;
import com.fastbee.device.domain.IotProduct;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 运维看板Mapper接口
 */
@Repository
public interface DeviceMonitorMapper
{

    //查询所有设备以及不同状态的设备数
    public DeviceStatusCount selectDeviceStatusCount(@Param("groupIds")Long[] groupIds);

    //查询仪表类产品下设备监控（在装）数
    public List<ProductDeviceCount> selectDeviceCounts(@Param("groupIds")Long[] groupIds);

    //查询所有产品下设备数量
    public List<ProductDeviceCount> selectDeviceCountByProductId();

    // 根据productId查询某个时间范围的故障数量
    public List<String> selectDeviceFaultsCount(@Param("groupIds")Long[] groupIds,
                                       @Param("productId") Long productId,
                                       @Param("beginTime") String beginTime,
                                       @Param("endTime") String endTime);

    // 根据groupId查询某个时间范围的故障数量
    public int selectCountByGroupId(@Param("groupId") Long groupId,
                                    @Param("beginTime") String beginTime,
                                    @Param("endTime") String endTime);

    //查询仪表类下的所有产品
    public List<IotProduct> selectIotProductLists();

    //查询所属单位列表
    public List<IotGroup> findGroupTree(@Param("groupName") String groupName);

    //根据groupId查询仪表类产品下设备监控（在装）数
    public int selectDeviceCountByGroupId(@Param("groupId") Long groupId);

    //根据groupId查询所有设备信息
    public List<IotDeviceInfo> selectDeviceInfoByGroupId(@Param("groupIds")Long[] groupIds);

    public int selectCountByProductId(@Param("groupIds")Long[] groupIds,
                                                    @Param("productId") Long productId);
    // 不在线设备编号
    public List<String> selectDeviceInfoByProductId(@Param("groupIds")Long[] groupIds,
                                                @Param("productId") Long productId);
    //根据分组id查询产品id
    public List<Long> selectProductId(@Param("categoryId") Long categoryId);

    //查询所有设备故障清单
    public List<DeviceFaultList> selectDeviceFaultsList();

    //根据SN查询设备的分组
    public List<DeviceFaultList> selectGroupNameBySN(@Param("serialNumber") String serialNumber);

    //根据SN查询设备的所属名
    public String selectBelongsDeviceNameBySN(@Param("serialNumber") String serialNumber);

    //根据SN查询设备在过去一个小时是否报警
    int selectFaultsCountBySN(@Param("serialNumber") String serialNumber);

}
