<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastbee.device.mapper.IotDeviceGroupMapper">
    
    <resultMap type="com.fastbee.device.domain.IotDeviceGroup" id="IotDeviceGroupResult">
        <result property="groupId"    column="group_id"    />
        <result property="groupName"    column="group_name"    />
        <result property="groupOrder"    column="group_order"    />
        <result property="parentId"    column="parent_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="remark"    column="remark"    />
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap type="com.fastbee.device.domain.IotGroupInput" id="IotGroupInputResult">
        <result property="id"    column="device_id"    />
    </resultMap>

    <sql id="selectdeviceGroupVo">
        select group_id, group_name, group_order, parent_id, del_flag, remark
             ,create_by, create_time, update_by, update_time from iot_group
    </sql>

    <select id="selectdeviceGroupList" parameterType="IotDeviceGroup" resultMap="IotDeviceGroupResult">
        <include refid="selectdeviceGroupVo"/>
        <where>
            <if test="groupName != null  and groupName != ''"> and group_name like concat('%', #{groupName}, '%')</if>
            <if test="groupOrder != null "> and group_order = #{groupOrder}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="groupId != null "> and group_id = #{groupId} or parent_id = #{groupId}</if>
        </where>
        order by group_order ,create_time desc
    </select>
    <select id="selectGroupByGroupName" parameterType="String" resultMap="IotDeviceGroupResult">
        <include refid="selectdeviceGroupVo"/>
        where group_name = #{groupName}
    </select>

    <select id="selectdeviceGroupByGroupId" parameterType="Long" resultMap="IotDeviceGroupResult">
        <include refid="selectdeviceGroupVo"/>
        where group_id = #{groupId}
    </select>
        
    <insert id="insertdeviceGroup" parameterType="IotDeviceGroup" useGeneratedKeys="true" keyProperty="groupId">
        insert into iot_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupName != null">group_name,</if>
            <if test="groupOrder != null">group_order,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupName != null">#{groupName},</if>
            <if test="groupOrder != null">#{groupOrder},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatedeviceGroup" parameterType="IotDeviceGroup">
        update iot_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupName != null">group_name = #{groupName},</if>
            <if test="groupOrder != null">group_order = #{groupOrder},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where group_id = #{groupId}
    </update>

    <delete id="deleteGroupByGroupId" parameterType="Long">
        delete from iot_group where group_id = #{groupId}
    </delete>

    <delete id="deleteDeviceGroupByGroupId" parameterType="Long">
        delete from iot_device_group where group_id = #{groupId}
    </delete>

    <delete id="deleteGroupByGroupIds" parameterType="String">
        delete from iot_group where group_id in
        <foreach item="groupId" collection="array" open="(" separator="," close=")">
            #{groupId}
        </foreach>
    </delete>

    <select id="selectDeviceIdsByGroupId" parameterType="Long" resultMap="IotGroupInputResult">
        select device_id from iot_device_group where group_id=#{groupId}
    </select>

    <select id="selectGroupByDeviceId" parameterType="Long" resultMap="IotDeviceGroupResult">
        select g.group_id, g.group_name from iot_group g
            left join iot_device_group dg
                ON g.group_id = dg.group_id
            where dg.device_id = #{deviceId}
    </select>

    <select id="findByParentId" parameterType="IotDeviceGroup" resultMap="IotDeviceGroupResult">
        select group_id, group_name, parent_id from iot_group
        <where>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
        </where>
    </select>

    <delete id="deleteDeviceGroupByGroupIds" parameterType="String">
        delete from iot_device_group where group_id in
        <foreach item="groupId" collection="array" open="(" separator="," close=")">
            #{groupId}
        </foreach>
    </delete>

    <insert id="insertDeviceGroups" parameterType="com.fastbee.device.domain.IotDeviceGroupInput">
        insert into iot_device_group (device_id,group_id)
        values
        <foreach item="deviceId" collection="deviceIds" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{deviceId},#{groupId},
            </trim>
        </foreach>
    </insert>

    <!-- 检查是否有子分类 -->
    <select id="hasChildByGroupId" resultType="int">
        SELECT COUNT(*)
        FROM iot_group
        WHERE parent_id = #{groupId}
    </select>

    <select id="selectGroupIdByGroupName" resultType="Long">
        SELECT group_id FROM iot_group WHERE group_name = #{groupName}
    </select>
</mapper>