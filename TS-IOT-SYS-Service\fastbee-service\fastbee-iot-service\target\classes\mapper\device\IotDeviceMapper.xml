<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastbee.device.mapper.IotDeviceMapper">
    
    <resultMap type="com.fastbee.device.domain.IotDevice" id="IotDeviceResult">
        <result property="deviceId"    column="device_id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="productId"    column="product_id"    />
        <result property="productName"    column="product_name"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="imgUrl"    column="img_url"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="remark"    column="remark"    />
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="activeTime"    column="active_time"    />
        <result property="deviceType"    column="device_type"    />
        <result property="modelId"    column="model_id"    />
        <result property="value"    column="value"    />
    </resultMap>

    <resultMap type="com.fastbee.device.domain.IotDeviceShortOutput" id="IotDeviceShortResult">
        <result property="deviceId"    column="device_id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="productId"    column="product_id"    />
        <result property="productName"    column="product_name"    />
        <result property="deviceType"    column="device_type"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="status"    column="status"    />
        <result property="activeTime"    column="active_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="imgUrl"    column="img_url"    />
    </resultMap>

    <resultMap type="com.fastbee.iot.model.ProductAuthenticateModel" id="DeviceAuthenticateResult">
        <result property="deviceId"    column="device_id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="status"    column="status"    />
        <result property="productId"    column="product_id"    />
        <result property="productName"    column="product_name"    />
        <result property="productStatus"    column="product_status"    />
        <result property="serialNumber"    column="serial_number"    />
    </resultMap>

    <resultMap type="com.fastbee.iot.model.DeviceRelateAlertLogVO" id="DeviceRelateAlertLogVOResult">
        <result property="deviceId" column="device_id"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="deviceName"    column="device_name"    />
        <result property="userId" column="user_id"/>
    </resultMap>

    <sql id="selectIotDeviceVo">
        select device_id, device_name, product_id, product_name, serial_number, img_url, status, del_flag, remark, active_time
             , create_by, create_time, update_by, update_time  from iot_device
    </sql>

    <select id="selectIotDeviceList" parameterType="IotDevice" resultMap="IotDeviceResult">
        select d.device_id, d.device_name, d.product_id, d.product_name, d.serial_number, d.img_url, status, d.del_flag, d.remark, d.active_time
        ,d.create_by, d.create_time, d.update_by, d.update_time
        from iot_device d
        <if test="groupId != null "> left join iot_device_group g on g.device_id=d.device_id </if>
        <where>
            <if test="groupId != null "> and g.group_id = #{groupId}</if>
            <if test="deviceId != null "> and d.device_id = #{deviceId}</if>
            <if test="deviceName != null  and deviceName != ''"> and d.device_name like concat('%', #{deviceName}, '%')</if>
            <if test="productId != null "> and d.product_id = #{productId}</if>
            <if test="serialNumber != null  and serialNumber != ''"> and d.serial_number = #{serialNumber}</if>
            <if test="imgUrl != null  and imgUrl != ''"> and d.img_url = #{imgUrl}</if>
            <if test="status != null "> and d.status = #{status}</if>
            <if test="status != null "> and g.group_id = #{groupId}</if>
        </where>
        order by d.create_time desc , d.serial_number
    </select>
    
    <select id="selectIotDeviceByDeviceId" parameterType="Long" resultMap="IotDeviceResult">
        <include refid="selectIotDeviceVo"/>
        where device_id = #{deviceId}
    </select>
        
    <insert id="insertIotDevice" parameterType="IotDevice" useGeneratedKeys="true" keyProperty="deviceId">
        insert into iot_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceName != null and deviceName != ''">device_name,</if>
            <if test="productId != null">product_id,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="serialNumber != null and serialNumber != ''">serial_number,</if>
            <if test="imgUrl != null">img_url,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="activeTime != null">active_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceName != null and deviceName != ''">#{deviceName},</if>
            <if test="productId != null">#{productId},</if>
            <if test="productName != null  and productName != ''">#{productName},</if>
            <if test="serialNumber != null  and serialNumber != ''">#{serialNumber},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="activeTime != null">#{activeTime},</if>
         </trim>
    </insert>

    <update id="updateIotDevice" parameterType="IotDevice">
        update iot_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceName != null and deviceName != ''">device_name = #{deviceName},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="serialNumber != null and serialNumber != ''">serial_number = #{serialNumber},</if>
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where device_id = #{deviceId}
    </update>

    <update id="updateDeviceStatus" parameterType="IotDevice">
        update iot_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="activeTime != null">active_time = #{activeTime},</if>
            <if test="updateTime !=null">update_time = #{updateTime,jdbcType=TIMESTAMP}</if>
        </trim>
        where serial_number = #{serialNumber}
    </update>

    <delete id="deleteIotDeviceByDeviceId" parameterType="Long">
        delete from iot_device where device_id = #{deviceId}
    </delete>

    <delete id="deleteIotDeviceByDeviceIds" parameterType="String">
        delete from iot_device where device_id in 
        <foreach item="deviceId" collection="array" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </delete>

    <select id="getDeviceNumCount" parameterType="String" resultType="int">
        select count(device_id) from iot_device where serial_number = #{deviceNum}
    </select>

    <select id="selectProductAuthenticate" parameterType="com.fastbee.iot.model.AuthenticateInputModel" resultMap="DeviceAuthenticateResult">
        SELECT p.product_id,p.product_name,p.STATUS as product_status,d.device_id,d.device_name,d.STATUS,d.serial_number
        FROM iot_product p
                 LEFT JOIN ( SELECT device_id, device_name, STATUS, product_id, product_name, serial_number
                             FROM iot_device
                             WHERE serial_number = #{serialNumber} ) AS d ON d.product_id = p.product_id
        WHERE
            p.product_id = #{productId}
    </select>

    <select id="selectDeviceByDeviceId" parameterType="Long" resultMap="IotDeviceResult">
        select d.device_id, d.device_name, d.product_id, d.serial_number, d.img_url
             , d.active_time, d.create_time, d.update_time, d.remark, p.product_name,p.device_type
        from iot_device d
        left join iot_product p on p.product_id=d.product_id
        where device_id = #{deviceId}
    </select>
    <select id="selectDeviceShortList" parameterType="com.fastbee.device.domain.IotDevice" resultMap="IotDeviceShortResult">
        select DISTINCT d.device_id, d.device_name, d.product_id, p.product_name, d.serial_number, COALESCE(NULLIF(d.img_url, ''), p.img_url) img_url, d.status, d.del_flag, d.remark
             , d.active_time, d.create_time, d.update_time, p.device_type
        from iot_device d
        left join iot_product p on p.product_id=d.product_id
<!--        <if test="groupId != null "> left join iot_device_group g on g.device_id=d.device_id </if>-->
        <if test="groupIds != null and groupIds.size() > 0">
            LEFT JOIN
            iot_device_group g ON g.device_id = d.device_id
        </if>
        <where>
            <if test="groupIds != null and groupIds.size() > 0">
                AND g.group_id IN
                <foreach collection="groupIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
<!--            <if test="groupId != null "> and g.group_id = #{groupId}</if>-->
            <if test="deviceName != null  and deviceName != ''"> and d.device_name like concat('%', #{deviceName}, '%')</if>
            <if test="productId != null "> and d.product_id = #{productId}</if>
            <if test="deviceType != null "> and p.device_type = #{deviceType}</if>
            <if test="productName != null  and productName != ''"> and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="serialNumber != null  and serialNumber != ''"> and d.serial_number = #{serialNumber}</if>
            <if test="status != null "> and d.status = #{status}</if>
            <if test="params.beginActiveTime != null and params.beginActiveTime != '' and params.endActiveTime != null and params.endActiveTime != ''"> and d.active_time between #{params.beginActiveTime} and #{params.endActiveTime}</if>
        </where>
        order by d.create_time desc , d.serial_number
    </select>

    <select id="selectDeviceBySerialNumber" parameterType="String" resultMap="IotDeviceResult">
        <include refid="selectIotDeviceVo"/>
        where serial_number = #{serialNumber}
    </select>

    <delete id="deleteDeviceGroupByDeviceId" parameterType="Long">
        delete from iot_device_group where device_id = #{deviceId}
    </delete>

    <select id="selectIotDeviceByModelId" parameterType="Long" resultMap="IotDeviceResult">
        select d.device_id, d.device_name,t.model_id,t.value,t.identifier
        from iot_things_model_value t
        left join iot_device d on d.device_id=t.device_id
        where  t.model_id = #{modelId}
    </select>

    <select id="selectSerialNumberByProductId" parameterType="Long" resultType="String">
        select serial_number from iot_device where product_id = #{productId}
    </select>


    <select id="selectDeviceRunningStatusByDeviceId" parameterType="Long" resultMap="IotDeviceShortResult">
        select d.device_id, d.device_name, d.product_id, p.product_name, d.serial_number, d.img_url, d.status, d.del_flag, d.remark
             , d.active_time, d.create_time, d.update_time, p.device_type
        from iot_device d
        left join iot_product p on p.product_id=d.product_id
        where d.device_id = #{deviceId}
    </select>

    <update id="resetDeviceStatus" parameterType="String">
        -- 设备状态（1-未激活，2-禁用，3-在线，4-离线）
        update iot_device set status=4
        where serial_number = #{serialNumber} and status = 3
    </update>

    <update id="reSetDeviceStatus">
        update iot_device set status = 4 where  status = 3
    </update>

    <update id="batchChangeOnline">
        update iot_device d
        set d.status = 3,
        d.update_time = now()
        where d.serial_number in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>

    </update>

    <update id="batchChangeOffline">
        update iot_device d
        set d.status = 4,
        d.update_time = now()
        where d.serial_number in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getDeviceNumsByProductId" parameterType="Long" resultType="String">
        select serial_number from iot_device
        where product_id = #{productId}
    </select>

    <select id="selectDeviceActive" resultType="com.fastbee.iot.model.DeviceStatusVO">
        select d.status , d.serial_number as serialNumber, d.product_id , p.device_type
        from iot_device d inner join iot_product p on d.product_id = p.product_id
        where d.status in (3,4)
          and d.del_flag = '0' and p.del_flag = '0'
    </select>

    <select id="checkExistBySerialNumbers" resultType="java.lang.String">
        select serial_number
        from iot_device
        where serial_number in
        <foreach collection="serialNumberList" item="serialNumber" open="(" separator="," close=")">
            #{serialNumber}
        </foreach>
    </select>

    <insert id="insertBatchDevice" parameterType="com.fastbee.device.domain.IotDevice"  useGeneratedKeys="true" keyProperty="deviceId">
        insert into iot_device (device_name, product_id, product_name, serial_number, create_by, create_time)
        values
        <foreach collection="deviceList" item="device" separator=",">
            (#{device.deviceName},
            #{device.productId},
            #{device.productName},
            #{device.serialNumber},
            #{device.createBy},
            sysdate())
        </foreach>
    </insert>

    <select id="selectDeviceCountByProductId" parameterType="Long" resultType="Integer">
        select count(device_id) from iot_device where product_id = #{productId}
    </select>

    <select id="selectDevicesByProductId" resultMap="IotDeviceResult">
        select d.serial_number,
        d.device_name
        from iot_device d
        where d.product_id = #{productId,jdbcType=BIGINT}
    </select>

    <select id="selectDeviceBySerialNumbers" resultMap="DeviceRelateAlertLogVOResult">
        select device_id, serial_number, device_name, user_id
        from iot_device
        where serial_number in
        <foreach collection="deviceNumbers" item="deviceNumber" index="index" open="(" separator="," close=")">
            #{deviceNumber}
        </foreach>
    </select>
</mapper>