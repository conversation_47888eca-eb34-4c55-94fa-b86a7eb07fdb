package com.fastbee.data.controller.test;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class SysVersionController {

    @GetMapping("/system/version")
    public VersionInfo getVersionInfo() {
        return new VersionInfo("v0.1.10", "2024-05-02 15:44:00");
    }

    // 定义一个内部静态类来封装版本信息
    public static class VersionInfo {
        private String version;
        private String compileTime;

        public VersionInfo(String version, String compileTime) {
            this.version = version;
            this.compileTime = compileTime;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getCompileTime() {
            return compileTime;
        }

        public void setCompileTime(String compileTime) {
            this.compileTime = compileTime;
        }
    }
}