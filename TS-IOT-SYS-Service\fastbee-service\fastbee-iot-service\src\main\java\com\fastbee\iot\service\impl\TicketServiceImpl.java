package com.fastbee.iot.service.impl;

import com.fastbee.common.core.domain.entity.SysUser;
import com.fastbee.common.utils.DateUtils;
import com.fastbee.iot.domain.Ticket;
import com.fastbee.iot.service.ITicketService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.fastbee.iot.mapper.TicketMapper;

import java.util.List;

import static com.fastbee.common.utils.SecurityUtils.getLoginUser;

/**
 * 设备告警Service业务层处理
 */
@Service
public class TicketServiceImpl implements ITicketService
{
    @Autowired
    private TicketMapper ticketMapper;

    /**
     * 查询工单
     * @param ticketId 工单主键
     * @return 工单
     */
    @Override
    public Ticket selectTicketByTicketId(Long ticketId)
    {
        return ticketMapper.selectTicketByTicketId(ticketId);
    }

    /**
     * 查询工单列表
     * @param ticket 工单
     * @return 工单集合
     */
    @Override
    public List<Ticket> selectTicketList(Ticket ticket)
    {
        return ticketMapper.selectTicketList(ticket);
    }

    /**
     * 新增工单
     * @param ticket 工单
     * @return 结果
     */
    @Override
    public int insertTicket(Ticket ticket)
    {
        SysUser user = getLoginUser().getUser();
        ticket.setCreateBy(user.getUserName());
        ticket.setCreateTime(DateUtils.getNowDate());
        return ticketMapper.insertTicket(ticket);
    }

    /**
     * 修改工单
     * @param ticket 工单
     * @return 结果
     */
    @Override
    public int updateTicket(Ticket ticket)
    {
        SysUser user = getLoginUser().getUser();
        ticket.setUpdateBy(user.getUserName());
        ticket.setUpdateTime(DateUtils.getNowDate());
        return ticketMapper.updateTicket(ticket);
    }

    /**
     * 批量删除工单
     * @param ticketIds 需要删除的数据主键集合
     * @return 结果
     */
    @Override
    public int deleteTicketByTicketIds(Long[] ticketIds)
    {
        return ticketMapper.deleteTicketByTicketIds(ticketIds);
    }

    /**
     * 删除工单
     * @param ticketId 工单主键
     * @return 结果
     */
    @Override
    public int deleteTicketByTicketId(Long ticketId)
    {
        return ticketMapper.deleteTicketByTicketId(ticketId);
    }

}
