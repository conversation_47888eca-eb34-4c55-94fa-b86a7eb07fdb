{"groups": [{"name": "sip", "type": "com.fastbee.media.conf.IotSysSipConfig", "sourceType": "com.fastbee.media.conf.IotSysSipConfig"}, {"name": "spring.datasource.druid.tdengine-server", "type": "javax.sql.DataSource", "sourceType": "com.fastbee.iot.tdengine.config.TDengineConfig", "sourceMethod": "tdengineDataSource()"}], "properties": [{"name": "sip.domain", "type": "java.lang.String", "sourceType": "com.fastbee.media.conf.IotSysSipConfig"}, {"name": "sip.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.fastbee.media.conf.IotSysSipConfig", "defaultValue": false}, {"name": "sip.id", "type": "java.lang.String", "sourceType": "com.fastbee.media.conf.IotSysSipConfig"}, {"name": "sip.ip", "type": "java.lang.String", "sourceType": "com.fastbee.media.conf.IotSysSipConfig"}, {"name": "sip.log", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.fastbee.media.conf.IotSysSipConfig", "defaultValue": false}, {"name": "sip.password", "type": "java.lang.String", "sourceType": "com.fastbee.media.conf.IotSysSipConfig"}, {"name": "sip.port", "type": "java.lang.Integer", "sourceType": "com.fastbee.media.conf.IotSysSipConfig"}, {"name": "sip.zlm-record-path", "type": "java.lang.String", "sourceType": "com.fastbee.media.conf.IotSysSipConfig"}], "hints": []}