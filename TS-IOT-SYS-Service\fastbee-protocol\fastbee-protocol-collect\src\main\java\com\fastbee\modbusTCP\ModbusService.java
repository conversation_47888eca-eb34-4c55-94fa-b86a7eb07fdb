package com.fastbee.modbusTCP;


import com.fastbee.common.core.protocol.modbus.ModbusCode;
import com.fastbee.common.enums.ModbusDataType;
import javax.annotation.Resource;
import java.io.IOException;
import java.nio.ByteBuffer;

public class ModbusService {

    @Resource
    private ModbusTcpClient modbusTcpClient;

    public void performOperation(ModbusDevice device) throws IOException {
        ModbusCode modbusCode = ModbusCode.getInstance(device.getCode());
        switch (modbusCode) {
            case Read01:
                boolean[] coilStatus = readCoilStatus(device);
                printData(coilStatus);
                break;
            case Read02:
                boolean[] inputStatus = readInputStatus(device);
                printData(inputStatus);
                break;
            case Read03:
                Object holdingRegisters = readHoldingRegisters(device);
                printData(holdingRegisters);
                break;
            case Read04:
                Object inputRegisters = readInputRegisters(device);
                printData(inputRegisters);
                break;
            case Write05:
                writeSingleCoil(device, true);
                break;
            case Write06:
                writeSingleHoldingRegister(device, 0);
                break;
            case Write0F:
                writeMultipleCoils(device, new boolean[]{true, false, true});
                break;
            case Write10:
                writeMultipleHoldingRegisters(device, new int[]{1, 2, 3});
                break;
        }
    }

    private void printData(Object data) {
        if (data instanceof boolean[]) {
            boolean[] booleanArray = (boolean[]) data;
            for (boolean value : booleanArray) {
                System.out.print(value + " ");
            }
            System.out.println();
        } else if (data instanceof short[]) {
            short[] shortArray = (short[]) data;
            for (short value : shortArray) {
                System.out.print(value + " ");
            }
            System.out.println();
        } else if (data instanceof long[]) {
            long[] longArray = (long[]) data;
            for (long value : longArray) {
                System.out.print(value + " ");
            }
            System.out.println();
        } else if (data instanceof float[]) {
            float[] floatArray = (float[]) data;
            for (float value : floatArray) {
                System.out.print(value+ " ");
            }
            System.out.println();
        } else {
            System.out.println(data);
        }
    }

    // 功能码 01：读取线圈状态
    private boolean[] readCoilStatus(ModbusDevice device) throws IOException {
        return (boolean[]) modbusTcpClient.performReadOperation(device, (byte) 0x01, ModbusDataType.BIT);
    }

    // 功能码 02：读取输入状态
    private boolean[] readInputStatus(ModbusDevice device) throws IOException {
        return (boolean[]) modbusTcpClient.performReadOperation(device, (byte) 0x02, ModbusDataType.BIT);
    }

    // 功能码 03：读取保存寄存器
    private Object readHoldingRegisters(ModbusDevice device) throws IOException {
        return modbusTcpClient.performReadOperation(device, (byte) 0x03, ModbusDataType.convert(device.getParseType()));
    }

    // 功能码 04：读取输入寄存器
    private Object readInputRegisters(ModbusDevice device) throws IOException {
        return modbusTcpClient.performReadOperation(device, (byte) 0x04, ModbusDataType.convert(device.getParseType()));
    }

    // 功能码 05：写入单个线圈寄存器
    private void writeSingleCoil(ModbusDevice device, boolean value) throws IOException {
        modbusTcpClient.performWriteOperation(device, (byte) 0x05, value? (short) 0xFF00 : (short) 0x0000);
    }

    // 功能码 06：写入单个保存寄存器
    private void writeSingleHoldingRegister(ModbusDevice device, int value) throws IOException {
        modbusTcpClient.performWriteOperation(device, (byte) 0x06, (short) value);
    }

    // 功能码 15：写入多个线圈状态
    private void writeMultipleCoils(ModbusDevice device, boolean[] values) throws IOException {
        int byteCount = (values.length + 7) / 8;
        byte[] coilBytes = new byte[byteCount];
        for (int i = 0; i < values.length; i++) {
            int byteIndex = i / 8;
            int bitIndex = i % 8;
            coilBytes[byteIndex] |= (values[i]? 1 : 0) << (7 - bitIndex);
        }
        modbusTcpClient.performWriteOperation(device, (byte) 0x0F, coilBytes);
    }

    // 功能码 16：写入多个保存寄存器
    private void writeMultipleHoldingRegisters(ModbusDevice device, int[] values) throws IOException {
        int byteCount = values.length * 2;
        ByteBuffer valueBuffer = ByteBuffer.allocate(byteCount);
        for (int value : values) {
            valueBuffer.putShort((short) value);
        }
        modbusTcpClient.performWriteOperation(device, (byte) 0x10, valueBuffer.array());
    }

}
