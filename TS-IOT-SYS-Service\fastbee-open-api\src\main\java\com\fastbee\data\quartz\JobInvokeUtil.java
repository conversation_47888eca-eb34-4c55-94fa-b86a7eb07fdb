package com.fastbee.data.quartz;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fastbee.common.core.mq.InvokeReqDto;
import com.fastbee.common.core.redis.RedisCache;
import com.fastbee.common.core.redis.RedisKeyBuilder;
import com.fastbee.common.core.thingsModel.ThingsModelSimpleItem;
import com.fastbee.common.utils.DateUtils;
import com.fastbee.common.utils.spring.SpringUtils;
import com.fastbee.iot.domain.*;
import com.fastbee.iot.mapper.*;
import com.fastbee.iot.model.Action;
import com.fastbee.iot.model.DeviceRelateAlertLogVO;
import com.fastbee.mq.ruleEngine.SceneContext;
import com.fastbee.mq.service.IFunctionInvoke;
import com.fastbee.mq.service.IMqttMessagePublish;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 任务执行工具
 *
 * <AUTHOR>
 */
public class JobInvokeUtil {

    /**
     * 获取消息推送接口
     */
    private static IMqttMessagePublish messagePublish = SpringUtils.getBean(IMqttMessagePublish.class);

    private static SceneMapper sceneMapper = SpringUtils.getBean(SceneMapper.class);

    private static FlowExecutor flowExecutor = SpringUtils.getBean(FlowExecutor.class);

    /**
     * 执行方法
     *
     * @param deviceJob 系统任务
     */
    public static void invokeMethod(DeviceJob deviceJob) throws Exception {
        if (deviceJob.getJobType() == 1) {
            System.out.println("------------------------执行定时任务-----------------------------");
            List<Action> actions = JSON.parseArray(deviceJob.getActions(), Action.class);
            List<ThingsModelSimpleItem> propertys = new ArrayList<>();
            List<ThingsModelSimpleItem> functions = new ArrayList<>();
            for (int i = 0; i < actions.size(); i++) {
                ThingsModelSimpleItem model = new ThingsModelSimpleItem();
                model.setId(actions.get(i).getId());
                model.setValue(actions.get(i).getValue());
                model.setRemark("设备定时");
                if (actions.get(i).getType() == 1) {
                    propertys.add(model);
                } else if (actions.get(i).getType() == 2) {
                    functions.add(model);
                }
            }
            // 发布属性
            if (propertys.size() > 0) {
                messagePublish.publishProperty(deviceJob.getProductId(), deviceJob.getSerialNumber(), propertys, 0);
            }
            // 发布功能
            if (functions.size() > 0) {
                messagePublish.publishFunction(deviceJob.getProductId(), deviceJob.getSerialNumber(), functions, 0);
            }

        } else if (deviceJob.getJobType() == 3) {
            System.out.println("------------------[定时执行场景联动]---------------------");
            Scene scene=sceneMapper.selectSceneBySceneId(deviceJob.getSceneId());
            // 执行场景规则,异步非阻塞
            SceneContext context = new SceneContext("", 0L,0,null);
            Future<LiteflowResponse> future= flowExecutor.execute2Future(String.valueOf(scene.getChainName()), null, context);
            if (!future.get().isSuccess()) {
                Exception e = future.get().getCause();
                System.out.println("场景联动执行失败 message= " + e.getMessage());
            }
        }
    }

}
