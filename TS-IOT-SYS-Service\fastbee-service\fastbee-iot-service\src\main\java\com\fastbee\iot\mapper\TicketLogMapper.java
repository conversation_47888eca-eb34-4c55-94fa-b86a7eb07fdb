package com.fastbee.iot.mapper;

import com.fastbee.iot.domain.TicketLog;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TicketLogMapper
{
    /**
     * 查询工时
     * @param logId 工时主键
     * @return 工时
     */
    public TicketLog selectTicketLogByLogId(Long logId);

    /**
     * 查询工时列表
     * @param ticketLog 工时
     * @return 工单集合
     */
    public List<TicketLog> selectTicketLogList(TicketLog ticketLog);

    /**
     * 新增工时
     * @param ticketLog 工时
     * @return 结果
     */
    public int insertTicketLog(TicketLog ticketLog);

    /**
     * 修改工时
     * @param ticketLog 工时
     * @return 结果
     */
    public int updateTicketLog(TicketLog ticketLog);

    /**
     * 删除工时
     * @param logId 工时主键
     * @return 结果
     */
    public int deleteTicketLogByLogId(Long logId);

    /**
     * 批量删除工时
     * @param logIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTicketLogByLogIds(Long[] logIds);
}
