package com.fastbee.device.controller;

import com.fastbee.common.annotation.Log;
import com.fastbee.common.core.controller.BaseController;
import com.fastbee.common.core.domain.AjaxResult;
import com.fastbee.common.core.page.TableDataInfo;
import com.fastbee.common.enums.BusinessType;
import com.fastbee.iot.domain.TicketLog;
import com.fastbee.iot.service.ITicketLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@Api(tags = "工时模块")
@RestController
@RequestMapping("/iot/ticketLog")
public class TicketLogController extends BaseController
{
    @Autowired
    private ITicketLogService ticketLogService;

    /**
     * 查询工时列表
     */
    @ApiOperation("查询工时列表")
    @PreAuthorize("@ss.hasPermi('iot:ticketLog:list')")
    @GetMapping("/list")
    public TableDataInfo list(TicketLog ticketLog)
    {
        startPage();
        List<TicketLog> list = ticketLogService.selectTicketLogList(ticketLog);
        return getDataTable(list);
    }

    /**
     * 查询工时
     */
    @ApiOperation("查询工时")
    @PreAuthorize("@ss.hasPermi('iot:ticketLog:query')")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") Long logId)
    {
        return AjaxResult.success(ticketLogService.selectTicketLogByLogId(logId));
    }

    /**
     * 新增工时
     */
    @ApiOperation("新增工时")
    @PreAuthorize("@ss.hasPermi('iot:ticketLog:add')")
    @Log(title = "新增工时", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TicketLog ticketLog)
    {
        return toAjax(ticketLogService.insertTicketLog(ticketLog));
    }

    /**
     * 修改工时
     */
    @ApiOperation("修改工时")
    @PreAuthorize("@ss.hasPermi('iot:ticketLog:edit')")
    @Log(title = "修改工时", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TicketLog ticketLog)
    {
        return toAjax(ticketLogService.updateTicketLog(ticketLog));
    }

    /**
     * 删除工时
     */
    @ApiOperation("删除工时")
    @PreAuthorize("@ss.hasPermi('iot:ticketLog:remove')")
    @Log(title = "删除工时", businessType = BusinessType.DELETE)
	@DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds)
    {
        return toAjax(ticketLogService.deleteTicketLogByLogIds(logIds));
    }

}
