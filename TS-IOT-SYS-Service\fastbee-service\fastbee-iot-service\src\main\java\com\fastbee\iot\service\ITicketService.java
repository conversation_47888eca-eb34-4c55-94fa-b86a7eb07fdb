package com.fastbee.iot.service;

import com.fastbee.iot.domain.Ticket;
import java.util.List;

/**
 * 工单Service接口
 */
public interface ITicketService
{
    /**
     * 查询工单
     * @param ticketId 工单主键
     * @return 工单
     */
    public Ticket selectTicketByTicketId(Long ticketId);

    /**
     * 查询工单列表
     * @param ticket 工单
     * @return 工单集合
     */
    public List<Ticket> selectTicketList(Ticket ticket);

    /**
     * 新增工单
     * @param ticket 工单
     * @return 结果
     */
    public int insertTicket(Ticket ticket);

    /**
     * 修改工单
     * @param ticket 工单
     * @return 结果
     */
    public int updateTicket(Ticket ticket);

    /**
     * 删除工单
     * @param ticketId 工单主键
     * @return 结果
     */
    public int deleteTicketByTicketId(Long ticketId);

    /**
     * 批量删除工单
     * @param ticketIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTicketByTicketIds(Long[] ticketIds);
}
