SHOW CREATE TABLE `fastbee5`.`category_tree_view`;
SHOW CREATE TABLE `fastbee5`.`gen_table`;
SHOW CREATE TABLE `fastbee5`.`gen_table_column`;
SHOW CREATE TABLE `fastbee5`.`iot_alert`;
SHOW CREATE TABLE `fastbee5`.`iot_alert_log`;
SHOW CREATE TABLE `fastbee5`.`iot_alert_notify_template`;
SHOW CREATE TABLE `fastbee5`.`iot_alert_scene`;
SHOW CREATE TABLE `fastbee5`.`iot_category`;
SHOW CREATE TABLE `fastbee5`.`iot_device`;
SHOW CREATE TABLE `fastbee5`.`iot_device_group`;
SHOW CREATE TABLE `fastbee5`.`iot_device_job`;
SHOW CREATE TABLE `fastbee5`.`iot_device_log`;
SHOW CREATE TABLE `fastbee5`.`iot_device_template`;
SHOW CREATE TABLE `fastbee5`.`iot_device_user`;
SHOW CREATE TABLE `fastbee5`.`iot_event_log`;
SHOW CREATE TABLE `fastbee5`.`iot_firmware`;
SHOW CREATE TABLE `fastbee5`.`iot_firmware_task`;
SHOW CREATE TABLE `fastbee5`.`iot_firmware_task_detail`;
SHOW CREATE TABLE `fastbee5`.`iot_function_log`;
SHOW CREATE TABLE `fastbee5`.`iot_goview_project`;
SHOW CREATE TABLE `fastbee5`.`iot_goview_project_data`;
SHOW CREATE TABLE `fastbee5`.`iot_group`;
SHOW CREATE TABLE `fastbee5`.`iot_product`;
SHOW CREATE TABLE `fastbee5`.`iot_product_authorize`;
SHOW CREATE TABLE `fastbee5`.`iot_product_thingsmodels`;
SHOW CREATE TABLE `fastbee5`.`iot_protocol`;
SHOW CREATE TABLE `fastbee5`.`iot_scene`;
SHOW CREATE TABLE `fastbee5`.`iot_scene_device`;
SHOW CREATE TABLE `fastbee5`.`iot_scene_script`;
SHOW CREATE TABLE `fastbee5`.`iot_script`;
SHOW CREATE TABLE `fastbee5`.`iot_simulate_log`;
SHOW CREATE TABLE `fastbee5`.`iot_social_platform`;
SHOW CREATE TABLE `fastbee5`.`iot_social_user`;
SHOW CREATE TABLE `fastbee5`.`iot_things_model`;
SHOW CREATE TABLE `fastbee5`.`iot_things_model_template`;
SHOW CREATE TABLE `fastbee5`.`iot_things_model_value`;
SHOW CREATE TABLE `fastbee5`.`iot_user`;
SHOW CREATE TABLE `fastbee5`.`iot_var_temp`;
SHOW CREATE TABLE `fastbee5`.`iot_var_temp_salve`;
SHOW CREATE TABLE `fastbee5`.`media_server`;
SHOW CREATE TABLE `fastbee5`.`news`;
SHOW CREATE TABLE `fastbee5`.`news_category`;
SHOW CREATE TABLE `fastbee5`.`notify_channel`;
SHOW CREATE TABLE `fastbee5`.`notify_log`;
SHOW CREATE TABLE `fastbee5`.`notify_template`;
SHOW CREATE TABLE `fastbee5`.`oauth_access_token`;
SHOW CREATE TABLE `fastbee5`.`oauth_approvals`;
SHOW CREATE TABLE `fastbee5`.`oauth_client_details`;
SHOW CREATE TABLE `fastbee5`.`oauth_client_token`;
SHOW CREATE TABLE `fastbee5`.`oauth_code`;
SHOW CREATE TABLE `fastbee5`.`oauth_refresh_token`;
SHOW CREATE TABLE `fastbee5`.`oss_config`;
SHOW CREATE TABLE `fastbee5`.`oss_detail`;
SHOW CREATE TABLE `fastbee5`.`qrtz_blob_triggers`;
SHOW CREATE TABLE `fastbee5`.`qrtz_calendars`;
SHOW CREATE TABLE `fastbee5`.`qrtz_cron_triggers`;
SHOW CREATE TABLE `fastbee5`.`qrtz_fired_triggers`;
SHOW CREATE TABLE `fastbee5`.`qrtz_job_details`;
SHOW CREATE TABLE `fastbee5`.`qrtz_locks`;
SHOW CREATE TABLE `fastbee5`.`qrtz_paused_trigger_grps`;
SHOW CREATE TABLE `fastbee5`.`qrtz_scheduler_state`;
SHOW CREATE TABLE `fastbee5`.`qrtz_simple_triggers`;
SHOW CREATE TABLE `fastbee5`.`qrtz_simprop_triggers`;
SHOW CREATE TABLE `fastbee5`.`qrtz_triggers`;
SHOW CREATE TABLE `fastbee5`.`scada`;
SHOW CREATE TABLE `fastbee5`.`scada_component`;
SHOW CREATE TABLE `fastbee5`.`scada_device_bind`;
SHOW CREATE TABLE `fastbee5`.`scada_echart`;
SHOW CREATE TABLE `fastbee5`.`scada_gallery`;
SHOW CREATE TABLE `fastbee5`.`scada_model`;
SHOW CREATE TABLE `fastbee5`.`sip_config`;
SHOW CREATE TABLE `fastbee5`.`sip_device`;
SHOW CREATE TABLE `fastbee5`.`sip_device_channel`;
SHOW CREATE TABLE `fastbee5`.`sys_auth_user`;
SHOW CREATE TABLE `fastbee5`.`sys_config`;
SHOW CREATE TABLE `fastbee5`.`sys_dept`;
SHOW CREATE TABLE `fastbee5`.`sys_dict_data`;
SHOW CREATE TABLE `fastbee5`.`sys_dict_type`;
SHOW CREATE TABLE `fastbee5`.`sys_job`;
SHOW CREATE TABLE `fastbee5`.`sys_job_log`;
SHOW CREATE TABLE `fastbee5`.`sys_logininfor`;
SHOW CREATE TABLE `fastbee5`.`sys_menu`;
SHOW CREATE TABLE `fastbee5`.`sys_notice`;
SHOW CREATE TABLE `fastbee5`.`sys_oper_log`;
SHOW CREATE TABLE `fastbee5`.`sys_post`;
SHOW CREATE TABLE `fastbee5`.`sys_role`;
SHOW CREATE TABLE `fastbee5`.`sys_role_dept`;
SHOW CREATE TABLE `fastbee5`.`sys_role_menu`;
SHOW CREATE TABLE `fastbee5`.`sys_user`;
SHOW CREATE TABLE `fastbee5`.`sys_user_post`;
SHOW CREATE TABLE `fastbee5`.`sys_user_role`;