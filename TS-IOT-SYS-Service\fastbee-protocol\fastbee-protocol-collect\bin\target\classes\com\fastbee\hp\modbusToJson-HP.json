1. 上报数据格式:
  device1：   从机1标识
  name：      物模型标识符
  value：     上报值
 {
    "device1": [
                {
            "name": "J2",
            "value": 8.331631
        },
        {
            "name": "J1",
            "value": -130.123718
        }
    ],
    "device2": [
        {
            "name": "J4",
            "value": -16.350224
        },
        {
            "name": "J3",
            "value": 94.769806
        }
    ]
 }

2. 下发报文格式
  device   从机编号
  name     标识符
  value    值
  serNo    流水号
  {
    "device": 1,
    "name": "template",
    "value": 111,
    "serNo": "213245489543789"
  }

3. 下发指令回复格式
  serNo   平台的流水号，用于对应回复消息
  ack     下发指令状态 0是失败 1是成功
  {
   "serNo": "213245489543789",
   "ack": 1
  }
