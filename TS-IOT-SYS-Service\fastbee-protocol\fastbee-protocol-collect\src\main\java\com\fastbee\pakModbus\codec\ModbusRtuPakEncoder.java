package com.fastbee.pakModbus.codec;

import com.fastbee.common.core.mq.message.DeviceDownMessage;
import com.fastbee.pakModbus.model.PakModbusRtu;
import com.fastbee.protocol.WModelManager;
import com.fastbee.protocol.base.model.ActiveModel;
import com.fastbee.protocol.util.ArrayMap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import io.netty.buffer.PooledByteBufAllocator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/11/15 11:34
 */
@Component
@Slf4j
public class ModbusRtuPakEncoder {

    private static final ByteBufAllocator ALLOC = PooledByteBufAllocator.DEFAULT;

    @Autowired
    private WModelManager modelManager;
    private ArrayMap<ActiveModel> headerSchemaMap;

    public ModbusRtuPakEncoder(String...basePackages) {
        this.modelManager = new WModelManager(basePackages);
        this.headerSchemaMap = this.modelManager.getActiveMap(PakModbusRtu.class);
    }


    /**
     * 组装下发指令
     * @param pakModbusRtu
     * @return
     */
    public ByteBuf encode(PakModbusRtu pakModbusRtu){
        this.build();
        ByteBuf buf =  ALLOC.buffer();
        ActiveModel activeModel = headerSchemaMap.get(2);
        activeModel.writeTo(buf,pakModbusRtu,null);
        return buf;
    }

    private void build(){
        if (this.headerSchemaMap == null) {
            this.headerSchemaMap = this.modelManager.getActiveMap(PakModbusRtu.class);
        }
    }
}
