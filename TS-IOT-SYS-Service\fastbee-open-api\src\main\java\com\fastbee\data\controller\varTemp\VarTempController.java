package com.fastbee.data.controller.varTemp;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.fastbee.iot.service.IDeviceTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.fastbee.common.annotation.Log;
import com.fastbee.common.core.controller.BaseController;
import com.fastbee.common.core.domain.AjaxResult;
import com.fastbee.common.enums.BusinessType;
import com.fastbee.iot.domain.VarTemp;
import com.fastbee.iot.service.IVarTempService;
import com.fastbee.common.utils.poi.ExcelUtil;
import com.fastbee.common.core.page.TableDataInfo;

/**
 * 设备采集变量模板Controller
 * 
 * <AUTHOR>
 * @date 2022-11-30
 */
@Api(tags = "设备采集变量模板")
@RestController
@RequestMapping("/iot/temp")
public class VarTempController extends BaseController
{
    @Autowired
    private IVarTempService varTempService;
    @Autowired
    private IDeviceTemplateService deviceTemplateService;


    /**
     * 查询设备采集变量模板列表
     */
    @ApiOperation("查询设备采集变量模板列表")
    @PreAuthorize("@ss.hasPermi('iot:temp:list')")
    @GetMapping("/list")
    public TableDataInfo list(VarTemp varTemp)
    {
        startPage();
        List<VarTemp> list = varTempService.selectVarTempList(varTemp);
        return getDataTable(list);
    }

    /**
     * 导出设备采集变量模板列表
     */
    @ApiOperation("导出设备采集变量模板列表")
    @PreAuthorize("@ss.hasPermi('iot:temp:export')")
    @Log(title = "设备采集变量模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VarTemp varTemp)
    {
        List<VarTemp> list = varTempService.selectVarTempList(varTemp);
        ExcelUtil<VarTemp> util = new ExcelUtil<VarTemp>(VarTemp.class);
        util.exportExcel(response, list, "设备采集变量模板数据");
    }

    /**
     * 获取设备采集变量模板详细信息
     */
    @ApiOperation("获取设备采集变量模板详细信息")
    @PreAuthorize("@ss.hasPermi('iot:temp:query')")
    @GetMapping(value = "/{templateId}")
    public AjaxResult getInfo(@PathVariable("templateId") Long templateId)
    {
        return AjaxResult.success(varTempService.selectVarTempByTemplateId(templateId));
    }

    /**
     * 新增设备采集变量模板
     */
    @ApiOperation("新增设备采集变量模板")
    @PreAuthorize("@ss.hasPermi('iot:temp:add')")
    @Log(title = "设备采集变量模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VarTemp varTemp)
    {
        return AjaxResult.success(varTempService.insertVarTemp(varTemp));
    }

    /**
     * 修改设备采集变量模板
     */
    @ApiOperation("修改设备采集变量模板")
    @PreAuthorize("@ss.hasPermi('iot:temp:edit')")
    @Log(title = "设备采集变量模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VarTemp varTemp)
    {
        return toAjax(varTempService.updateVarTemp(varTemp));
    }

    /**
     * 删除设备采集变量模板
     */
    @ApiOperation("删除设备采集变量模板")
    @PreAuthorize("@ss.hasPermi('iot:temp:remove')")
    @Log(title = "设备采集变量模板", businessType = BusinessType.DELETE)
	@DeleteMapping("/{templateIds}")
    public AjaxResult remove(@PathVariable Long[] templateIds)
    {
        return toAjax(varTempService.deleteVarTempByTemplateIds(templateIds));
    }

    @ApiOperation("根据产品id查询采集点模板")
    @GetMapping("/getTemp")
    public AjaxResult getTemp(Long productId){
        return AjaxResult.success(deviceTemplateService.selectDeviceTemplateByProduct(productId));
    }

    @ApiOperation("根据产品id获取模板详情")
    @GetMapping("/getTempByPid")
    public AjaxResult getTempByPid(Long productId){
        return AjaxResult.success(varTempService.selectVarTempByProductId(productId));
    }
}
