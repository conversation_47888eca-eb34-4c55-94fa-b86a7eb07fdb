<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>fastbee-gateway</artifactId>
		<groupId>com.fastbee</groupId>
		<version>3.8.5</version>
	</parent>
	<artifactId>fastbee-mq</artifactId>

	<dependencies>

		<dependency>
			<groupId>org.apache.rocketmq</groupId>
			<artifactId>rocketmq-spring-boot-starter</artifactId>
			<version>2.2.1</version>
		</dependency>
		<dependency>
			<groupId>com.fastbee</groupId>
			<artifactId>fastbee-protocol-base</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fastbee</groupId>
			<artifactId>fastbee-protocol-collect</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fastbee</groupId>
			<artifactId>fastbee-mqtt-client</artifactId>
			<version>3.8.5</version>
			<scope>compile</scope>
		</dependency>

		<dependency>
			<groupId>com.fastbee</groupId>
			<artifactId>fastbee-notify-core</artifactId>
		</dependency>

	</dependencies>


</project>
