# 物模型功能设计精简版

## 1. 物模型相关功能ER图

```mermaid
erDiagram
    IOT_CATEGORY ||--o{ IOT_PRODUCT : contains
    IOT_PRODUCT }o--o{  IOT_Product_ThingsModels : uses
    IOT_Product_ThingsModels }o--o{  IOT_Things_Model : defines
    IOT_PRODUCT ||--o{ IOT_DEVICE : contains
    IOT_DEVICE ||--o{ IOT_Things_Model_Value : has_value_for
    IOT_DEVICE ||--o{ IOT_DEVICE_LOG : logs_telemetry_for
    IOT_GROUP }o--o{ IOT_DEVICE_GROUP  : contains
    IOT_DEVICE_GROUP }o--o{ IOT_DEVICE  : contains
    IOT_USER }o--o{ IOT_DEVICE_USER  : owns
    IOT_DEVICE_USER }o--o{ IOT_DEVICE  : owns
```

## 2. 实体说明

### 2.1 IOT_CATEGORY (产品类型)
- 产品类型具有树形结构，可包含多个产品

### 2.2 IOT_PRODUCT (产品)
- 一个产品可包含多个设备
- 每个产品对应一套物模型属性模板
- 产品类型：直连设备、网关设备、网关子设备、监控设备、固定设备、虚拟设备

### 2.3 IOT_Things_Model (物模型参数定义)
物模型是产品数字化的描述，分为以下类型：

- **属性**：描述设备信息和状态
  - 固有属性：生产厂家、出厂编号等
  - 运维属性：安装位置、检修周期等
  - 遥测属性：实时压力值、流量等
  - 通讯属性：IP、端口、寄存器地址等
  - 虚拟属性：数字孪生相关参数
- **服务**：设备可被外部调用的能力或方法
- **事件**：设备主动上报的信息、告警和故障
- **关系**：描述设备之间的物理关系和逻辑关系

### 2.4 IOT_DEVICE (设备)
- 每个设备拥有多个物模型参数值
- 设备的物模型参数需要实例化

### 2.5 IOT_Things_Model_Value (物模型参数的值)
- 存储设备物模型参数的具体值（不包含遥测属性值）

### 2.6 IOT_DEVICE_LOG (遥测属性实时记录)
- 存储设备遥测属性的连续时序数据

### 2.7 IOT_GROUP (设备分组)
- 设备可以属于多个分组

### 2.8 IOT_USER (设备业主用户)
- 设备只能属于一个用户，但用户可拥有多个设备

## 3. 数据库设计

### 主要表结构

#### IOT_CATEGORY
- `category_id`: 产品分类ID (PK)
- `category_name`: 产品分类名称
- `parent_id`: 父级ID
- `order_num`: 显示顺序

#### IOT_PRODUCT
- `product_id`: 产品ID (PK)
- `product_name`: 产品名称
- `category_id`: 产品分类ID
- `status`: 状态（1-未发布，2-已发布）
- `device_type`: 产品类型

#### IOT_Things_Model
- `model_id`: 物模型参数ID (PK)
- `model_name`: 物模型参数名称
- `identifier`: 标识符（物模型参数英文名）
- `type`: 物模型参数类型
- `sub_type`: 参数子类型
- `datatype`: 数据类型
- `specs`: 数据定义（JSON格式）

#### IOT_DEVICE
- `device_id`: 设备ID (PK)
- `device_name`: 设备名称
- `product_id`: 产品ID
- `serial_number`: 设备编号（唯一）
- `status`: 设备状态（1-未激活，2-禁用，3-在线，4-离线）

#### IOT_Things_Model_Value
- `id`: 索引ID (PK)
- `device_id`: 设备ID
- `model_id`: 物模型参数ID
- `value`: 参数值
- `identifier`: 标识符

#### IOT_DEVICE_LOG
- `log_id`: 设备遥测属性记录ID (PK)
- `identifier`: 标识符
- `serial_number`: 设备编号
- `log_type`: 日志类型
- `log_value`: 日志值
- `log_time`: 日志记录时间

## 4. 功能实现示例

### 4.1 物模型参数管理

```sql
-- 插入物模型属性
INSERT INTO iot_things_model (model_name, identifier, type, sub_type, datatype, specs) 
VALUES ('生产厂家', 'manufacturer', 1, 1, 'string', NULL);

-- 查询特定类型的物模型参数
SELECT * FROM IOT_Things_Model WHERE type = 1 AND sub_type = 3;
```

### 4.2 产品管理

```sql
-- 创建产品
INSERT INTO IOT_PRODUCT (product_name, category_id, status, device_type)
VALUES ('智能灯泡', 1, 2, 1);

-- 关联产品与物模型参数
INSERT INTO IOT_Product_ThingsModels (product_id, model_id)
VALUES (1, 1), (1, 2);
```

### 4.3 设备管理

```sql
-- 创建设备
INSERT INTO IOT_DEVICE (device_name, product_id, serial_number, status)
VALUES ('客厅灯泡', 1, 'BULB001', 3);

-- 设置设备物模型参数值
INSERT INTO IOT_Things_Model_Value (device_id, model_id, value, identifier)
VALUES (1, 1, '生产厂家A', 'manufacturer');

-- 记录设备遥测数据
INSERT INTO IOT_DEVICE_LOG (identifier, serial_number, log_type, log_value, log_time)
VALUES ('power', 'BULB001', 1, '75', NOW());
```

### 4.4 用户与设备关联

```sql
-- 关联用户与设备
INSERT INTO IOT_DEVICE_USER (user_id, device_id, is_owner, perms)
VALUES (1, 1, 1, 'read,write');
```

## 5. 扩展思考

- 遥测数据可迁移至时序数据库以提高性能
- 物模型参数可设置权限控制，限制不同用户的访问范围
- 可通过配置模板简化产品与物模型的关联 