package com.fastbee.device.controller;

import com.alibaba.fastjson2.JSONObject;
import com.fastbee.common.core.controller.BaseController;
import com.fastbee.common.core.domain.AjaxResult;
import com.fastbee.device.domain.DeviceMonitor.*;
import com.fastbee.device.service.IDeviceMonitorService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;
import java.util.Map;

/**
 * 物联网运维管理平台
 * 运维看板
 */
@RestController
@RequestMapping("/deviceMonitor")
public class DeviceMonitorController extends BaseController {

    @Autowired
    private IDeviceMonitorService deviceMonitorService;

    @GetMapping("/scope")
    @ApiOperation("设备监测范围")
    public AjaxResult scope() {
        List<JSONObject> result = deviceMonitorService.scope();
        return AjaxResult.success(result);
    }

    @GetMapping("/dynamics")
    @ApiOperation("设备监测动态")
    public AjaxResult dynamics() {
        List<Map<String, String>> result = deviceMonitorService.dynamics();
        return AjaxResult.success(result);
    }

    @GetMapping("/faults/classification")
    @ApiOperation("设备故障分类对比")
    public AjaxResult classification() {
        Map<String, Object> result = deviceMonitorService.classification();
        return AjaxResult.success(result);
    }

    @GetMapping("/completenessRate")
    @ApiOperation("采集齐全率分析")
    public AjaxResult completenessRate() {
        List<Map<String, String>> result = deviceMonitorService.completenessRate();
        return AjaxResult.success(result);
    }

    @GetMapping("/faults/weeklyComparison")
    @ApiOperation("设备故障周对比")
    public AjaxResult weeklyComparison() {
        Map<String, Object> result = deviceMonitorService.weeklyComparison();
        return AjaxResult.success(result);
    }

    @GetMapping("/operationStats")
    @ApiOperation("设备运行统计")
    public AjaxResult operationStats(IotGroup iotGroup) {
        List<DeviceOperationStats> result = deviceMonitorService.operationStats(iotGroup);
        return AjaxResult.success(result);
    }

    @GetMapping("/maintenanceEvaluation")
    @ApiOperation("设备运维考核")
    public AjaxResult maintenanceEvaluation() {
        List<DeviceFaultStatistics> result = deviceMonitorService.maintenanceEvaluation();
        return AjaxResult.success(result);
    }

    @GetMapping("/faults/areaRanking")
    @ApiOperation("设备故障区域排名")
    public AjaxResult areaRanking() {
        List<Map<String, String>> result = deviceMonitorService.areaRanking();
        return AjaxResult.success(result);
    }
    @GetMapping("/faults/list")
    @ApiOperation("设备故障清单")
    public AjaxResult faultsList() {
        List<DeviceFaultList> list = deviceMonitorService.faultsList();
        return AjaxResult.success(list);
    }

    @GetMapping("/groupTree")
    @ApiOperation("查询所属单位列表")
    public AjaxResult getGroupTree() {
        List<IotGroup> result = deviceMonitorService.findGroupTree();
        return AjaxResult.success(result);
    }


}
