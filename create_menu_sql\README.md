# 数据库SQL工具集合

这个目录包含了用于管理和操作MySQL数据库的各种脚本和SQL文件，主要针对`fastbee5`数据库。

## 文件说明

### SQL文件
- **编写sql**: 包含菜单表的SQL插入语句，用于创建基础菜单结构
- **编写sql2**: 包含优化后的菜单SQL语句，使用三级菜单结构
- **表结构**: 包含`sys_menu`表的结构定义
- **样例**: 菜单SQL插入的简单样例

### 工具脚本
- **show**: 包含91个`SHOW CREATE TABLE`语句，用于查询数据库中所有表的结构
- **fetch_table_ddl.py**: Python脚本，用于执行show文件中的语句并将结果汇总到单个文件
- **all_tables_ddl.txt**: 包含所有表的DDL语句，由fetch_table_ddl.py生成

## 使用方法

### 获取所有表的SHOW CREATE TABLE语句
可以使用以下SQL语句生成所有表的SHOW CREATE TABLE语句：

```sql
SELECT 
    CONCAT('SHOW CREATE TABLE `', table_schema, '`.`', table_name, '`;') AS show_create_statement
FROM 
    information_schema.tables 
WHERE 
    table_schema = 'fastbee5'
ORDER BY 
    table_name;
```

执行这个SQL会生成类似`show`文件中的内容，列出所有表的SHOW CREATE TABLE语句。

### 查看表结构
1. 确保已安装Python和mysql-connector-python包
   ```bash
   pip install mysql-connector-python
   ```

2. 修改`fetch_table_ddl.py`中的数据库连接信息
   ```python
   config = {
       'host': 'localhost',  # 数据库主机名
       'port': 5981,         # 端口号
       'user': 'root',       # 用户名
       'password': '123456', # 密码
       'database': 'fastbee5'# 数据库名
   }
   ```

3. 运行脚本获取所有表结构
   ```bash
   python fetch_table_ddl.py
   ```

4. 查看生成的`all_tables_ddl.txt`文件获取所有表的DDL定义

### 菜单管理
1. 使用`编写sql`或`编写sql2`中的SQL语句来创建或更新菜单
2. 可参考`表结构`了解菜单表的结构
3. 执行SQL语句前，确保数据库连接正确且有足够权限

## 注意事项
- 所有SQL语句执行前请先备份数据库
- 修改菜单IDs时，注意避免与自增ID冲突
- 对于长菜单名称，已在`编写sql2`中进行了优化处理

## 目录结构
```
create_menu_sql/
├── .venv/                  # Python虚拟环境
├── all_tables_ddl.txt      # 所有表结构定义
├── fetch_table_ddl.py      # 获取表结构的Python脚本
├── README.md               # 本文档
├── show                    # SHOW CREATE TABLE语句集合
├── 编写sql                 # 基础菜单SQL
├── 编写sql2                # 优化后的菜单SQL
├── 样例                    # SQL样例
└── 表结构                  # sys_menu表结构
``` 