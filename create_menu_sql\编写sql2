-- 菜单SQL插入语句

-- 主菜单插入（使用固定ID）
INSERT INTO fastbee5.sys_menu (menu_id, menu_name,parent_id,order_num,`path`,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
	(9000, '知识库',0,11,'kb',NULL,NULL,1,0,'M','0','0','','circular','admin',NOW(),'admin',NOW(),''),
	(9001, '大语言模型',0,12,'llm',NULL,NULL,1,0,'M','0','0','','circular','admin',NOW(),'admin',NOW(),''),
	(9002, '数字孪生',0,31,'dt',NULL,NULL,1,0,'M','0','0','','circular','admin',NOW(),'admin',NOW(),''),
	(9003, '物联网数据查询',0,14,'iotq',NULL,NULL,1,0,'M','0','0','','circular','admin',NOW(),'admin',NOW(),''),
	(9004, '智能体工具列表',0,15,'agtool',NULL,NULL,1,0,'M','0','0','','circular','admin',NOW(),'admin',NOW(),''),
	(9005, '智能体',0,16,'agent',NULL,NULL,1,0,'M','0','0','','circular','admin',NOW(),'admin',NOW(),''),
	(9006, '显卡资源监控',0,17,'gpu',NULL,NULL,1,0,'M','0','0','','circular','admin',NOW(),'admin',NOW(),'');

-- 知识库子菜单
INSERT INTO fastbee5.sys_menu (menu_name,parent_id,order_num,`path`,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
	('知识库管理',9000,1,'ai/kb/kbm','ai/kb/kbm',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('文件管理',9000,2,'ai/kb/fm','ai/kb/fm',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('文件向量化',9000,3,'ai/kb/fv','ai/kb/fv',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('知识库检索',9000,4,'ai/kb/kbs','ai/kb/kbs',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,'');

-- 大语言模型子菜单
INSERT INTO fastbee5.sys_menu (menu_name,parent_id,order_num,`path`,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
	('聊天',9001,1,'ai/llm/chat','ai/llm/chat',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('语音识别模块',9001,2,'ai/llm/asr','ai/llm/asr',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,'');

-- 数字孪生子菜单
INSERT INTO fastbee5.sys_menu (menu_name,parent_id,order_num,`path`,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
	('数字孪生场景',9002,1,'ai/dt/scene','ai/dt/scene',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('场景模型数据绑定',9002,2,'ai/dt/binding','ai/dt/binding',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('工艺流程演示',9002,3,'ai/dt/process','ai/dt/process',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('测量模块',9002,4,'ai/dt/measure','ai/dt/measure',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,'');

-- 物联网数据查询子菜单
INSERT INTO fastbee5.sys_menu (menu_name,parent_id,order_num,`path`,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
	('实时数据查询',9003,1,'ai/iotq/realtime','ai/iotq/realtime',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('报警规则设置',9003,2,'ai/iotq/alert','ai/iotq/alert',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('智能报警分析',9003,3,'ai/iotq/prompt','ai/iotq/prompt',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,'');

-- 智能体工具列表子菜单
INSERT INTO fastbee5.sys_menu (menu_name,parent_id,order_num,`path`,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
	('已有工具列表查询',9004,1,'ai/agtool/list','ai/agtool/list',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('工具需求待开发列表维护',9004,2,'ai/agtool/dev','ai/agtool/dev',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,'');

-- 智能体子菜单（二级菜单）
INSERT INTO fastbee5.sys_menu (menu_id, menu_name,parent_id,order_num,`path`,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
	(54321001, '智能体基础管理',9005,1,'base',NULL,NULL,1,0,'M','0','0','','circular','admin',NOW(),'admin',NOW(),''),
	(54321002, '提示词智能体',9005,2,'prompt',NULL,NULL,1,0,'M','0','0','','circular','admin',NOW(),'admin',NOW(),''),
	(54321003, '数据库智能体',9005,3,'db',NULL,NULL,1,0,'M','0','0','','circular','admin',NOW(),'admin',NOW(),''),
	(54321004, '视觉智能体',9005,4,'vision',NULL,NULL,1,0,'M','0','0','','circular','admin',NOW(),'admin',NOW(),''),
	(54321005, '图像识别智能体',9005,5,'image',NULL,NULL,1,0,'M','0','0','','circular','admin',NOW(),'admin',NOW(),'');

-- 智能体基础管理（三级菜单）
INSERT INTO fastbee5.sys_menu (menu_name,parent_id,order_num,`path`,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
	('智能体设置',54321001,1,'ai/agent/setting','ai/agent/setting',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('运行状态查询',54321001,2,'ai/agent/status','ai/agent/status',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('手动调用',54321001,3,'ai/agent/manual','ai/agent/manual',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('运行结果查询',54321001,4,'ai/agent/result','ai/agent/result',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,'');

-- 提示词智能体（三级菜单）
INSERT INTO fastbee5.sys_menu (menu_name,parent_id,order_num,`path`,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
	('工具调用配置',54321002,1,'ai/agent/prompt/tool','ai/agent/prompt/tool',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('智能体编排',54321002,2,'ai/agent/prompt/arrange','ai/agent/prompt/arrange',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('定时执行',54321002,3,'ai/agent/prompt/schedule','ai/agent/prompt/schedule',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('结果查询',54321002,4,'ai/agent/prompt/result','ai/agent/prompt/result',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,'');

-- 数据库智能体（三级菜单）
INSERT INTO fastbee5.sys_menu (menu_name,parent_id,order_num,`path`,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
	('基础配置',54321003,1,'ai/agent/db/config','ai/agent/db/config',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('表结构说明',54321003,2,'ai/agent/db/schema','ai/agent/db/schema',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('常用提示词',54321003,3,'ai/agent/db/prompt','ai/agent/db/prompt',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('查询结果维护',54321003,4,'ai/agent/db/result','ai/agent/db/result',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('定时任务',54321003,5,'ai/agent/db/schedule','ai/agent/db/schedule',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,'');

-- 视觉智能体（三级菜单）
INSERT INTO fastbee5.sys_menu (menu_name,parent_id,order_num,`path`,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
	('摄像头管理',54321004,1,'ai/agent/camera','ai/agent/camera',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('视频解码',54321004,2,'ai/agent/video/decode','ai/agent/video/decode',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('多路视频展示',54321004,3,'ai/agent/video/display','ai/agent/video/display',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('位置词汇关系',54321004,4,'ai/agent/camera/relation','ai/agent/camera/relation',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,'');

-- 图像识别智能体（三级菜单）
INSERT INTO fastbee5.sys_menu (menu_name,parent_id,order_num,`path`,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
	('功图识别',54321005,1,'ai/agent/diagram','ai/agent/diagram',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('图片上传',54321005,2,'ai/agent/image/upload','ai/agent/image/upload',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('图片标注',54321005,3,'ai/agent/image/label','ai/agent/image/label',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('训练结果查看',54321005,4,'ai/agent/image/train','ai/agent/image/train',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,''),
	('模型应用',54321005,5,'ai/agent/image/recognize','ai/agent/image/recognize',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,'');

-- 显卡资源监控子菜单
INSERT INTO fastbee5.sys_menu (menu_name,parent_id,order_num,`path`,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
	('显卡占用查询',9006,1,'ai/gpu/usage','ai/gpu/usage',NULL,1,0,'C','0','0',NULL,'circular','admin',NOW(),'',NULL,'');
