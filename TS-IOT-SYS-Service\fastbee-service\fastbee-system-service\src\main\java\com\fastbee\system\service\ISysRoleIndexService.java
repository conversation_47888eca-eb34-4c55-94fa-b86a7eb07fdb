package com.fastbee.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fastbee.common.core.domain.entity.SysRoleIndex;
import java.util.List;

/**
 * 角色首页配置
 */
public interface ISysRoleIndexService extends IService<SysRoleIndex> {
    /**
     * 查询角色首页配置列表
     */
    List<SysRoleIndex> selectRoleIndexList(SysRoleIndex sysRoleIndex);

    /**
     * 根据ID查询角色首页配置
     */
    SysRoleIndex selectRoleIndexById(String id);

    /**
     * 新增角色首页配置
     */
    int insertRoleIndex(SysRoleIndex sysRoleIndex);

    /**
     * 修改角色首页配置
     */
    int updateRoleIndex(SysRoleIndex sysRoleIndex);

    /**
     * 批量删除角色首页配置
     */
    int deleteRoleIndexByIds(String[] ids);

    /**
     * 删除角色首页配置信息
     */
    int deleteRoleIndexById(String id);

    /**
     * 查询默认首页
     */
    SysRoleIndex queryDefaultIndex();

    /**
     * 更新默认首页
     */
    boolean updateDefaultIndex(SysRoleIndex sysRoleIndex);

    /**
     * 创建最原始的默认首页配置
     */
    SysRoleIndex initDefaultIndex();

    /**
     * 清理默认首页的redis缓存
     */
    void cleanDefaultIndexCache();

    /**
     * 根据角色获取首页配置
     */
    SysRoleIndex getIndexByRoles(List<String> roles);
}