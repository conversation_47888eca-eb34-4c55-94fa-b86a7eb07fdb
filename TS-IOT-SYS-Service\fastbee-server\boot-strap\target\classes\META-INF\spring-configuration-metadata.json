{"groups": [{"name": "server.broker", "type": "com.fastbee.bootstrap.mqtt.MQTTBootStrap", "sourceType": "com.fastbee.bootstrap.mqtt.MQTTBootStrap"}, {"name": "server.tcp", "type": "com.fastbee.bootstrap.tcp.TCPBootStrap", "sourceType": "com.fastbee.bootstrap.tcp.TCPBootStrap"}, {"name": "server.udp", "type": "com.fastbee.bootstrap.udp.UDPBootStrap", "sourceType": "com.fastbee.bootstrap.udp.UDPBootStrap"}], "properties": [{"name": "server.broker.broker-node", "type": "java.lang.String", "sourceType": "com.fastbee.bootstrap.mqtt.MQTTBootStrap"}, {"name": "server.broker.keep-alive", "type": "java.lang.Integer", "sourceType": "com.fastbee.bootstrap.mqtt.MQTTBootStrap", "defaultValue": 0}, {"name": "server.broker.mqtt-server", "type": "com.fastbee.mqtt.server.MqttServer", "sourceType": "com.fastbee.bootstrap.mqtt.MQTTBootStrap"}, {"name": "server.broker.port", "type": "java.lang.Integer", "sourceType": "com.fastbee.bootstrap.mqtt.MQTTBootStrap", "defaultValue": 0}, {"name": "server.broker.web-socket-server", "type": "com.fastbee.mqtt.server.WebSocketServer", "sourceType": "com.fastbee.bootstrap.mqtt.MQTTBootStrap"}, {"name": "server.broker.websocket-path", "type": "java.lang.String", "sourceType": "com.fastbee.bootstrap.mqtt.MQTTBootStrap"}, {"name": "server.broker.websocket-port", "type": "java.lang.Integer", "sourceType": "com.fastbee.bootstrap.mqtt.MQTTBootStrap", "defaultValue": 0}, {"name": "server.tcp.delimiter", "type": "java.lang.Byte", "sourceType": "com.fastbee.bootstrap.tcp.TCPBootStrap", "defaultValue": 0}, {"name": "server.tcp.handler-interpolator", "type": "com.fastbee.bootstrap.tcp.config.TcpHandlerInterceptor", "sourceType": "com.fastbee.bootstrap.tcp.TCPBootStrap"}, {"name": "server.tcp.handler-mapping", "type": "com.fastbee.base.core.HandlerMapping", "sourceType": "com.fastbee.bootstrap.tcp.TCPBootStrap"}, {"name": "server.tcp.keep-alive", "type": "java.lang.Integer", "sourceType": "com.fastbee.bootstrap.tcp.TCPBootStrap", "defaultValue": 0}, {"name": "server.tcp.message-adapter", "type": "com.fastbee.modbus.codec.MessageAdapter", "sourceType": "com.fastbee.bootstrap.tcp.TCPBootStrap"}, {"name": "server.tcp.port", "type": "java.lang.Integer", "sourceType": "com.fastbee.bootstrap.tcp.TCPBootStrap", "defaultValue": 0}, {"name": "server.tcp.session-manager", "type": "com.fastbee.base.session.SessionManager", "sourceType": "com.fastbee.bootstrap.tcp.TCPBootStrap"}, {"name": "server.udp.delimiter", "type": "java.lang.Byte", "sourceType": "com.fastbee.bootstrap.udp.UDPBootStrap", "defaultValue": 0}, {"name": "server.udp.handler-interpolator", "type": "com.fastbee.bootstrap.tcp.config.TcpHandlerInterceptor", "sourceType": "com.fastbee.bootstrap.udp.UDPBootStrap"}, {"name": "server.udp.handler-mapping", "type": "com.fastbee.base.core.HandlerMapping", "sourceType": "com.fastbee.bootstrap.udp.UDPBootStrap"}, {"name": "server.udp.message-adapter", "type": "com.fastbee.modbus.codec.MessageAdapter", "sourceType": "com.fastbee.bootstrap.udp.UDPBootStrap"}, {"name": "server.udp.port", "type": "java.lang.Integer", "sourceType": "com.fastbee.bootstrap.udp.UDPBootStrap", "defaultValue": 0}, {"name": "server.udp.session-manager", "type": "com.fastbee.base.session.SessionManager", "sourceType": "com.fastbee.bootstrap.udp.UDPBootStrap"}], "hints": []}