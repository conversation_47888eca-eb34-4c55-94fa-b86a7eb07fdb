# TS-IOT-SYS-Service 后台目录结构及功能说明

## 项目概述

TS-IOT-SYS-Service 是基于 Fastbee V2.2 优化升级的物联网信息管理平台，通过国产化改造，实现了物联网现场中"采、存、治、算、用"的全流程管理。系统采用微服务架构设计，包含多个功能模块，支持边缘网关采集、物模型管理、规则引擎、监控视频流展示和数据组态等功能。

## 技术架构

* 后端：Spring Boot、Spring Security、Redis、JWT
* 前端：Vue3、VBen 框架、AntDesign UI
* 支持容器化部署

## 系统架构图

```mermaid
graph TD
    classDef mainModule fill:#f9f,stroke:#333,stroke-width:2px
    classDef subModule fill:#bbf,stroke:#333,stroke-width:1px
    
    A[TS-IOT-SYS-Service]
    
    A --> B[核心模块]
    A --> C[物联网功能模块]
    A --> D[扩展功能模块]
    
    B --> B1[系统管理<br>fastbee-admin]
    B --> B2[框架<br>fastbee-framework]
    B --> B3[公共模块<br>fastbee-common]
    
    C --> C1[业务服务<br>fastbee-service]
    C --> C2[服务集成<br>fastbee-server]
    C --> C3[协议解析<br>fastbee-protocol]
    C --> C4[网关模块<br>fastbee-gateway]
    C --> C5[监控与数据采集<br>fastbee-scada]
    C --> C6[录像模块<br>fastbee-record]
    C --> C7[通知模块<br>fastbee-notify]
    
    D --> D1[开放接口<br>fastbee-open-api]
    D --> D2[插件模块<br>fastbee-plugs]
    
    class A mainModule
    class B,C,D mainModule
    class B1,B2,B3,C1,C2,C3,C4,C5,C6,C7,D1,D2 subModule
```

## 系统数据流程图

```mermaid
graph TD
    classDef deviceLayer fill:#fcf,stroke:#333,stroke-width:1px
    classDef communicationLayer fill:#cff,stroke:#333,stroke-width:1px
    classDef businessLayer fill:#ffc,stroke:#333,stroke-width:1px
    classDef applicationLayer fill:#cfc,stroke:#333,stroke-width:1px
    classDef userLayer fill:#ccf,stroke:#333,stroke-width:1px
    
    A[物联网设备]
    B[网关模块]
    C[协议解析]
    D[业务层管理]
    E[数据存储]
    F[数据可视化]
    G[告警通知]
    H[管理用户]
    I[系统管理]
    J[第三方系统]
    K[开放接口]
    L[服务下发]
    
    A <--> B
    B --> C
    C --> D
    D --> E
    D --> F
    D --> G
    H --> I
    I --> D
    J --> K
    K --> D
    D --> L
    L --> B
    
    class A deviceLayer
    class B,C communicationLayer
    class D,L businessLayer
    class E,F,G applicationLayer
    class H,I,J,K userLayer
```

## 后台目录结构及功能

### 1. 核心模块

#### 1.1 fastbee-admin（系统管理模块）

管理系统的核心功能模块，包含系统用户、权限、配置等管理功能。负责处理系统的基础业务逻辑和管理功能。

**主要功能**：
- 用户管理：系统操作者配置
- 部门管理：组织机构配置（公司、部门、小组）
- 岗位管理：用户职务配置
- 菜单管理：系统菜单、操作权限配置
- 角色管理：角色权限分配与数据范围划分
- 字典管理：系统固定数据维护
- 参数管理：系统动态配置参数
- 通知公告：系统信息发布维护
- 日志管理：操作日志、登录日志记录与查询
- 在线用户监控：活跃用户状态监控
- 定时任务：在线任务调度与结果日志
- 代码生成：前后端代码生成（CRUD）
- 系统监控：CPU、内存、磁盘等资源监控
- 缓存监控：系统缓存信息查询
- 连接池监视：数据库连接池状态监控

#### 1.2 fastbee-framework（框架模块）

系统的基础框架模块，提供了项目的基础架构和通用功能支持。

**主要功能**：
- 系统安全框架
- 权限验证
- 异常处理
- 数据访问层封装
- Web层通用功能

#### 1.3 fastbee-common（公共模块）

提供系统各模块公共使用的工具类、常量、枚举等基础组件。

**主要功能**：
- 工具类库
- 常量定义
- 枚举定义
- 通用异常处理
- 公共配置

### 2. 物联网功能模块

#### 2.1 fastbee-service（业务层管理模块）

包含系统业务逻辑实现的核心模块，分为物联网业务服务和系统业务服务两部分。

**子模块**：
- **fastbee-iot-service**：物联网业务服务，处理设备管理、数据采集等物联网相关业务
- **fastbee-system-service**：系统业务服务，处理系统基础功能的业务逻辑

#### 2.2 fastbee-server（服务集成模块）

集成各种服务器组件，支持不同协议的通信服务。

**子模块**：
- **base-server**：服务基础模块
- **boot-strap**：服务启动模块
- **mqtt-broker**：MQTT协议服务
- **coap-server**：CoAP协议服务
- **iot-server-core**：服务核心，TCP/UDP服务搭建模块
- **sip-server**：SIP协议服务

#### 2.3 fastbee-protocol（协议解析模块）

处理各种物联网通信协议的解析和编码。

**子模块**：
- **fastbee-protocol-base**：协议基础模块
- **fastbee-protocol-collect**：数据采集协议模块，支持Modbus等工业协议

#### 2.4 fastbee-gateway（网关模块）

负责设备连接和数据转发的网关服务。

**子模块**：
- **gateway-boot**：网关启动模块
- **fastbee-mq**：消息队列模块，处理设备与平台间的消息通信

#### 2.5 fastbee-scada（监控与数据采集模块）

工业自动化控制系统，用于数据采集、监控和可视化。

**主要功能**：
- 组态管理
- 数据可视化
- 图形监控
- 历史数据查询

#### 2.6 fastbee-record（录像模块）

与ZLM（流媒体服务器）配合使用，提供视频录像控制和录像合并下载功能。

**主要功能**：
- 录像控制
- 录像合并
- 视频下载

#### 2.7 fastbee-notify（通知模块）

处理系统各类通知和告警信息。

**主要功能**：
- 告警通知
- 消息推送
- 事件通知

### 3. 扩展功能模块

#### 3.1 fastbee-open-api（开放接口模块）

提供系统对外开放的API接口服务。

**主要功能**：
- 第三方系统集成
- 开放接口管理
- API文档生成

#### 3.2 fastbee-plugs（插件模块）

提供系统扩展功能的插件集合。

**子模块**：
- **fastbee-http**：HTTP客户端
- **fastbee-oauth**：OAuth认证
- **fastbee-oss**：对象存储
- **fastbee-mqtt-client**：MQTT客户端
- **fastbee-generator**：代码生成
- **fastbee-quartz**：定时任务

## 核心功能说明

### 1. 物模型管理

物模型是对设备的数字化描述，定义了设备的属性、功能和事件。

**主要类**：
- `ThingsModel`：物模型基础类，包含物模型的基本属性
- `ThingsModelSimpleItem`：物模型简单项，用于数据传输

### 2. 设备管理

管理物联网设备的生命周期，包括设备注册、连接、状态监控等。

**核心功能**：
- 设备注册与认证
- 设备状态监控
- 设备数据采集
- 设备命令下发

### 3. 协议解析

支持多种物联网协议的解析和编码，如Modbus-RTU等。

**示例**：
- Modbus-RTU报文格式：
  - 上报：设备地址 + 命令号 + 返回数据字节数 + 数据 + CRC校验
  - 下发：设备地址 + 命令号 + 寄存器地址 + 数据位 + CRC校验

### 4. 网关通信

处理设备与平台之间的数据通信，支持多种通信方式。

**服务配置**：
- TCP服务：端口8888，支持保活机制
- UDP服务：可选端口8889，支持保活机制
- MQTT服务：可选配置broker服务

### 5. 数据采集与存储

采集设备数据并进行存储，支持历史数据查询和分析。

**功能**：
- 实时数据采集
- 数据存储（支持关系型数据库和时序数据库）
- 历史数据查询
- 数据统计分析

## 部署说明

系统支持容器化部署，使用Docker可快速部署整套系统。首次运行时需要：

1. 修改配置文件，确保各服务连接正常
2. 导入基础平台及SCADA平台的SQL脚本
3. 启动服务

## 配置参数

系统主要配置参数位于application.yml文件中：

- 服务端口：8080
- 上传路径：./uploadPath
- 设备心跳超时时间：120秒
- TCP服务端口：8888
- UDP服务端口：8889（可选）
- MQTT服务配置（可选）

## 总结

TS-IOT-SYS-Service后台系统采用模块化设计，各模块职责明确，通过微服务架构实现了高效、稳定、可扩展的物联网平台服务。系统既包含了完善的基础管理功能，又具备强大的物联网设备连接、数据采集和监控能力，可满足各类物联网应用场景的需求。 