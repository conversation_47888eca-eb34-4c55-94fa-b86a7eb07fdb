package com.fastbee;

import com.dtflys.forest.springboot.annotation.ForestScan;
import com.fastbee.framework.config.ProductMappingConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
@ForestScan(basePackages = "com.fastbee.media.client")
@EnableConfigurationProperties(ProductMappingConfig.class)
public class FastBeeApplication
{
    public static void main(String[] args)
    {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(FastBeeApplication.class, args);
    }
}
