package com.fastbee.data.controller.productModbus;

import com.fastbee.common.core.controller.BaseController;
import com.fastbee.common.core.page.TableDataInfo;
import com.fastbee.iot.domain.ModbusConfig;
import com.fastbee.iot.service.IModbusConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Modbus配置寄存器Controller
 */
@Api(tags = "Modbus配置寄存器")
@RestController
@RequestMapping("/modbus/config")
public class ModbusConfigController extends BaseController
{

    @Autowired
    private IModbusConfigService modbusConfigService;

    /**
     * 查询寄存器列表
     */
    @ApiOperation("查询寄存器列表")
    @PreAuthorize("@ss.hasPermi('modbus:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(ModbusConfig modbusConfig)
    {
        startPage();
        List<ModbusConfig> list = modbusConfigService.selectModbusConfigList(modbusConfig);;
        return getDataTable(list);
    }

    /**
     * 新增Modbus配置寄存器
     */
//    @ApiOperation("新增Modbus配置寄存器")
//    @PreAuthorize("@ss.hasPermi('modbus:config:addBatch')")
//    @Log(title = "Modbus配置寄存器", businessType = BusinessType.INSERT)
//    @PostMapping("/addBatch")
//    public AjaxResult add(@RequestBody ModbusConfig modbusConfig)
//    {
//        return AjaxResult.success(modbusConfigService.insertModbusConfig(modbusConfig));
//    }

    /**
     * 导出Modbus配置寄存器
     */
//    @ApiOperation("导出Modbus配置寄存器")
//    @PreAuthorize("@ss.hasPermi('iot:temp:exportModbus')")
//    @Log(title = "寄存器模板", businessType = BusinessType.EXPORT)
//    @PostMapping("/exportModbus")
//    public void export(HttpServletResponse response, ModbusConfig modbusConfig)
//    {
//        List<ModbusConfig> list = modbusConfigService.selectModbusConfigList(modbusConfig);
//        ExcelUtil<ModbusConfig> util = new ExcelUtil<ModbusConfig>(ModbusConfig.class);
//        util.exportExcel(response, list, "寄存器模板数据");
//    }

}
