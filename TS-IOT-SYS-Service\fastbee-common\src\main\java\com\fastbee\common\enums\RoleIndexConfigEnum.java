package com.fastbee.common.enums;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import java.util.List;

/**
 * 首页自定义
 * 通过角色编码与首页组件路径配置
 */
public enum RoleIndexConfigEnum {
    /**首页自定义 admin*/
    ADMIN("admin", "home/index"),
    /**首页自定义 hr*/
    HR("hr", "home/index"),
    /**首页自定义 manager*/
    MANAGER("manager", "home/index"),

    // 默认配置
    ROLE_INDEX_CONFIG_ENUM("RoleIndexConfigEnumDefault", "home/index");

    /**
     * 角色编码
     */
    String roleCode;
    /**
     * 路由index
     */
    String componentUrl;

    RoleIndexConfigEnum(String roleCode, String componentUrl) {
        this.roleCode = roleCode;
        this.componentUrl = componentUrl;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public String getComponentUrl() {
        return componentUrl;
    }

    /**
     * 通过角色获取首页组件
     */
    public static String getIndexByRoles(List<String> roles) {
        if (CollectionUtils.isEmpty(roles)) {
            return DefIndexConst.DEF_INDEX_COMPONENT;
        }

        for (RoleIndexConfigEnum item : RoleIndexConfigEnum.values()) {
            if (roles.contains(item.getRoleCode())) {
                return item.getComponentUrl();
            }
        }
        return DefIndexConst.DEF_INDEX_COMPONENT;
    }
}