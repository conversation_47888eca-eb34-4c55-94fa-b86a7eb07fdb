package com.fastbee.modbusTCP;

import lombok.extern.slf4j.Slf4j;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
public class ModbusProtocolService {

    private ScheduledExecutorService executorService;
    private ScheduledExecutorService timeoutExecutorService;

    @Resource
    private ModbusService modbusService;

    public void startPolling(ModbusDevice device) {
        executorService = Executors.newSingleThreadScheduledExecutor();
        timeoutExecutorService = Executors.newSingleThreadScheduledExecutor();
        Integer timerout = (device.getTimerout() == null || device.getTimerout() <= 0) ? 5 : device.getTimerout();
        executorService.scheduleAtFixedRate(() -> {
            try {
                modbusService.performOperation(device);
                // 成功接收到数据，重置超时任务
                timeoutExecutorService.schedule(() -> {
                    log.error("任务超时:"+ timerout+"minutes");
                    stopPolling();
                }, timerout, TimeUnit.MINUTES);
            } catch (IOException e) {
                log.error("执行Modbus操作时出错", e);
            }
        }, 0, device.getTimer(), TimeUnit.SECONDS);
    }

    public void stopPolling() {
        if (executorService!= null) {
            executorService.shutdown();
        }
        if (timeoutExecutorService!= null) {
            timeoutExecutorService.shutdown();
        }
    }
}