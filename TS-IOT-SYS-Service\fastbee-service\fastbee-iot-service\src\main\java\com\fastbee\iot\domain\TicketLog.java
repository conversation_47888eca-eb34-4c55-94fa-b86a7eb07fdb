package com.fastbee.iot.domain;

import com.fastbee.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "TicketLog", description = "工单实体 iot_ticket_time_log")
public class TicketLog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 工时记录ID */
    @ApiModelProperty("工时记录ID")
    private Long logId;

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    /** 工单ID */
    @ApiModelProperty("工单ID")
    private Long ticketId;

    /** 工时（小时） */
    @ApiModelProperty("工时（小时）")
    private BigDecimal hours;

    /** 记录人 */
    @ApiModelProperty("记录人")
    private String userId;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 记录时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date logTime;


    public Long getTicketId() {
        return ticketId;
    }

    public void setTicketId(Long ticketId) {
        this.ticketId = ticketId;
    }

    public BigDecimal getHours() {
        return hours;
    }

    public void setHours(BigDecimal hours) {
        this.hours = hours;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getLogTime() {
        return logTime;
    }

    public void setLogTime(Date logTime) {
        this.logTime = logTime;
    }
}
