package com.fastbee.device.domain.DeviceMonitor;

import com.fastbee.iot.domain.Scene;
import lombok.Data;
import java.util.Date;
/**
 * 统计设备故障清单
 */
@Data
public class DeviceFaultList
{
    //所属名
    private String belongsDeviceName;
    //设备名称
    private String deviceName;
    private Long productId;
    private String productName;
    //报警时间
    private Date alertTime;
    //所属单位
    private String groupName;
    //故障原因
    private String faultReason;
    //排查建议
    private String suggestion;

    /** 告警ID */
    private Long alertLogId;

    /** 告警名称 */
    private String alertName;

    /** 告警级别（1=提醒通知，2=轻微问题，3=严重警告，4=故障报警） */
    private Long alertLevel;

    /** 处理状态(0=不需要处理,1=未处理,2=已处理) */
    private Integer status;

    /** 设备编号 */
    private String serialNumber;

    /** 告警详情 */
    private String detail;
    private Date createTime;
    /** 脚本ID */
    private String scriptId;
    /** 场景ID*/
    private Long sceneId;

    private Scene scene;
}
