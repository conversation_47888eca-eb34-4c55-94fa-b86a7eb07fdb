package com.fastbee.device.service.impl;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fastbee.common.core.domain.AjaxResult;
import com.fastbee.common.core.domain.entity.SysUser;
import com.fastbee.common.core.domain.model.LoginUser;
import com.fastbee.common.core.mq.DeviceReport;
import com.fastbee.common.core.redis.RedisCache;
import com.fastbee.common.core.redis.RedisKeyBuilder;
import com.fastbee.common.core.thingsModel.ThingsModelSimpleItem;
import com.fastbee.common.core.thingsModel.ThingsModelValuesInput;
import com.fastbee.common.enums.DeviceStatus;
import com.fastbee.common.enums.ThingsModelType;
import com.fastbee.common.utils.CaculateUtils;
import com.fastbee.device.domain.*;
import com.fastbee.common.enums.DataEnum;
import com.fastbee.common.exception.ServiceException;
import com.fastbee.common.utils.DateUtils;
import com.fastbee.common.utils.StringUtils;
import com.fastbee.common.utils.json.JsonUtils;
import com.fastbee.device.domain.ThingsModel.*;
import com.fastbee.device.mapper.*;
import com.fastbee.device.service.IIotThingsModelService;
import com.fastbee.iot.domain.DeviceLog;
import com.fastbee.iot.domain.EventLog;
import com.fastbee.iot.domain.FunctionLog;
import com.fastbee.iot.mapper.EventLogMapper;
import com.fastbee.iot.model.AuthenticateInputModel;
import com.fastbee.iot.model.DeviceMqttConnectVO;
import com.fastbee.iot.model.DeviceStatusVO;
import com.fastbee.iot.model.ProductAuthenticateModel;
import com.fastbee.iot.service.*;
import com.fastbee.iot.service.cache.IDeviceCache;
import com.fastbee.iot.tdengine.service.ILogService;
import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.fastbee.device.service.IIotDeviceService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import javax.annotation.Resource;
import static com.fastbee.common.utils.SecurityUtils.getLoginUser;

/**
 * 设备管理Service业务层处理
 */
@Service
public class IotDeviceServiceImpl implements IIotDeviceService 
{
    private static final Logger log = LoggerFactory.getLogger(IotDeviceServiceImpl.class);
    @Autowired
    private IotDeviceMapper iotDeviceMapper;

    @Autowired
    private IToolService toolService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private IotDeviceModelMapper iotDeviceModelMapper;

    @Autowired
    private IotProductMapper iotProductMapper;

    @Autowired
    private IotDeviceUserMapper iotDeviceUserMapper;

    @Autowired
    private IIotThingsModelService iotThingsModelService;

    @Autowired
    private IotThingsModelServiceImpl iotThingsModelServiceImpl;

    @Autowired
    private IotThingsModelValueMapper iotThingsModelValueMapper;

    @Autowired
    private ILogService logService;

    @Resource
    private IEventLogService eventLogService;

    @Resource
    private IFunctionLogService functionLogService;

    @Resource
    private ISimulateLogService simulateLogService;

    @Autowired
    private IAlertLogService alertLogService;

    @Autowired
    private EventLogMapper eventLogMapper;

    @Value("${server.broker.port}")
    private Long brokerPort;

    @Resource
    @Lazy
    private IDeviceCache deviceCache;

    @Autowired
    private IotThingsModelMapper iotThingsModelMapper;

    @Autowired
    private IotDeviceGroupMapper iotDeviceGroupMapper;

    /**
     * 查询设备管理
     */
    @Override
    public IotDevice selectIotDeviceByDeviceId(Long deviceId)
    {
        IotDevice device = iotDeviceMapper.selectIotDeviceByDeviceId(deviceId);
        if(device==null){
            throw new ServiceException("未查询到该设备");
        }
        //获取遥测属性物模型
        List<IotThingsModelValueItem> list = getCacheDeviceStatus(device.getProductId(), device.getSerialNumber());
        if (list != null && list.size() > 0) {
            // redis中获取设备状态（物模型值）
            device.setThingsModelValue(JSONObject.toJSONString(list));
        }
        //获取属性物模型参数的值（不包含遥测属性）
        List<IotThingsModelValue> modelValue = iotThingsModelValueMapper.selectThingsModelValue(deviceId);
        device.setModelValue(modelValue);
        //查询子设备......
        if (device != null) {
            // 没图片用产品图片
            if (StringUtils.isEmpty(device.getImgUrl())) {
                device.setImgUrl(iotProductMapper.selectImgUrlByProductId(device.getProductId()));
            }
        }
        return device;
    }

    /**
     * 获取Redis缓存的设备全部状态（物模型值）
     */
    private List<IotThingsModelValueItem> getCacheDeviceStatus(Long productId, String deviceNumber) {
        String key = RedisKeyBuilder.buildTSLVCacheKey(productId, deviceNumber);
        List<IotThingsModelValueItem> valueList = new ArrayList<>();
        // 缓存设备状态（物模型值）到redis
        cacheDeviceStatus(productId, deviceNumber);
        Map<String, String> map = redisCache.hashEntity(key);
        // 获取redis缓存的物模型值
        valueList = map.values().stream().map(s -> JSONObject.parseObject(s, IotThingsModelValueItem.class))
                .collect(Collectors.toList());
//        if (map == null || map.size() == 0) {
//            // 缓存设备状态（物模型值）到redis
//            valueList = cacheDeviceStatus(productId, deviceNumber);
//        } else {
//            // 获取redis缓存的物模型值
//            valueList = map.values().stream().map(s -> JSONObject.parseObject(s, IotThingsModelValueItem.class))
//                    .collect(Collectors.toList());
//        }
        return valueList;
    }

//    // 判断缓存的值和当前的值是否不同
//    private boolean isValueChanged(List<IotThingsModelValueItem> cachedValueList, List<IotThingsModelValueItem> dbValueList) {
//        if (cachedValueList == null || dbValueList == null || cachedValueList.size() != dbValueList.size()) {
//            return true; // 长度不一致则认为有变化
//        }
//
//        for (int i = 0; i < cachedValueList.size(); i++) {
//            IotThingsModelValueItem cachedItem = cachedValueList.get(i);
//            IotThingsModelValueItem dbItem = dbValueList.get(i);
//
//            // 比较具体字段，判断值是否有变化
//            if (!cachedItem.equals(dbItem)) {
//                return true; // 如果有不同则返回 true
//            }
//        }
//        return false; // 如果没有变化则返回 false
//    }

    /**
     * 查询设备管理列表
     */
    @Override
    public List<IotDevice> selectIotDeviceList(IotDevice iotDevice)
    {
        return iotDeviceMapper.selectIotDeviceList(iotDevice);
    }

    /**
     * 新增设备管理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public IotDevice insertIotDevice(IotDevice iotDevice)
    {
        IotDevice existDevice = iotDeviceMapper.selectDeviceBySerialNumber(iotDevice.getSerialNumber());
        if (existDevice != null) {
            throw new ServiceException("设备编号：" + iotDevice.getSerialNumber() + " 已经存在，新增失败");
        }

        SysUser sysUser = getLoginUser().getUser();
        iotDevice.setCreateTime(DateUtils.getNowDate());
        iotDevice.setCreateBy(sysUser.getUserName());
        IotProduct product = iotProductMapper.selectIotProductByProductId(iotDevice.getProductId());
        if (product == null) {
            throw new ServiceException("产品不存在");
        }
        iotDevice.setImgUrl(product.getImgUrl());
        iotDevice.setProductName(product.getProductName());
        iotDeviceMapper.insertIotDevice(iotDevice);

        // 添加设备用户
        IotDeviceUser deviceUser = new IotDeviceUser();
        deviceUser.setUserId(sysUser.getUserId());
        deviceUser.setUserName(sysUser.getUserName());
        deviceUser.setPhonenumber(sysUser.getPhonenumber());
        deviceUser.setDeviceId(iotDevice.getDeviceId());
        deviceUser.setDeviceName(iotDevice.getDeviceName());
        deviceUser.setIsOwner(1);
        deviceUser.setCreateTime(DateUtils.getNowDate());
        deviceUser.setCreateBy(sysUser.getUserName());
        iotDeviceUserMapper.insertDeviceUser(deviceUser);
        // redis缓存设备默认状态（物模型值）
        cacheDeviceStatus(iotDevice.getProductId(), iotDevice.getSerialNumber());
        return iotDevice;
    }

    /**
     * 缓存设备状态到redis
     */
    @Transactional(rollbackFor = Exception.class)
    public List<IotThingsModelValueItem> cacheDeviceStatus(Long productId, String serialNumber) {
        // 获取物模型,设置默认值
        String thingsModels = iotThingsModelService.getCacheThingsModelByProductId(productId);
        JSONObject thingsModelObject = JSONObject.parseObject(thingsModels);
        //如果属性等4类不为空进行转换，否则报空指针
        List<IotThingsModelValueItem> valueList = convertJsonToValueList(thingsModelObject);
        // redis存储设备默认状态 键：产品ID_设备编号
        String key = RedisKeyBuilder.buildTSLVCacheKey(productId, serialNumber);
        IotDevice device = iotDeviceMapper.selectDeviceBySerialNumber(serialNumber);
        Long deviceId = device.getDeviceId();
        //获取属性物模型参数的值（不包含遥测属性）
        List<IotThingsModelValue> iotThingsModelValue = iotThingsModelValueMapper.selectThingsModelValue(deviceId);
        Map<String, IotThingsModelValue> thingsModelMap = iotThingsModelValue.stream()
                .collect(Collectors.toMap(IotThingsModelValue::getIdentifier, Function.identity()));
        //获取遥测属性
        Map<String, String> map = redisCache.hashEntity(key);
        Map<String, String> maps = new HashMap<>();
        for (IotThingsModelValueItem item : valueList) {
            processValueItem(item, maps);
        }
//        JSONArray propertiesArray = thingsModelObject.getJSONArray("properties");
//        //mqtt账号密码value为空，赋予物模型id
//        Long modelId = propertiesArray.stream()
//                .map(obj -> (JSONObject) obj)
//                .filter(property -> "mqtt".equals(property.getString("id")))
//                .map(property -> property.getLong("modelId"))
//                .findFirst()
//                .orElse(null);

        for (Map.Entry<String, String> entry : maps.entrySet()) {
            String modelKey = entry.getKey();
            String jsonString = entry.getValue();
            // 将jsonString解析为JSONObject
            JSONObject json = JSONObject.parseObject(jsonString);
            String value = json.getString("value");
            Long modelId = json.getLong("modelId");
            // 查找是否已有对应的 IotThingsModelValue 并赋值
            IotThingsModelValue thingsModel = thingsModelMap.get(modelKey);
            if (thingsModel != null) {
                value = thingsModel.getValue();
            }

            // mqtt账号密码 value为空，赋予默认值
            if (modelKey.startsWith("mqtt_")||modelKey.equals("device_status")) {
                if (value == null || value.isEmpty()) {
                    value = getDefaultValueForKey(modelKey, productId, serialNumber);
                    // 创建 IotThingsModelValue 对象并设置属性
                    IotThingsModelValue modelValue = createMqttModelValue(modelKey, value, modelId,deviceId);
                    if (value != null && !value.isEmpty()) {
                        if (thingsModelMap.containsKey(modelKey)) {
                            iotThingsModelValueMapper.updateMqttValue(modelValue);
                        } else {
                            iotThingsModelValueMapper.insertMqttValue(modelValue);
                        }
                    }
                }
            }

            // 如果 value 为空，则使用 Redis 中的原始值
            if (value == null || value.trim().isEmpty()) {
                // 从原始 Redis 数据中获取原值
                String original = map.get(modelKey);
                try {
                    // 先判断原始字符串是否合法
                    if (StringUtils.isBlank(original)) {
                        continue;
                    }
                    JSONObject originalJson = JSONObject.parseObject(original);
                    if (originalJson == null) {
                        continue;
                    }
                    String ts = originalJson.getString("ts");
                    if (StringUtils.isNotBlank(ts)) {
                        maps.put(modelKey, original);
                        continue;
                    } else {
                        log.warn("字段 'ts' 为空，原始内容为：{}", original);
                    }
                } catch (Exception e) {
                    log.error("处理 JSON 数据时发生异常，内容为：{}", original, e);
                }
            }
            // 更新 json 中的 value 字段并重新放入 maps
            json.put("value", value);
            maps.put(modelKey, JSONObject.toJSONString(json));
        }

        redisCache.hashPutAll(key, maps);
        return valueList;
    }

    // 创建modelValue对象
    private IotThingsModelValue createMqttModelValue(String mqttKey, String value, Long modelId,Long deviceId) {
        IotThingsModelValue modelValue = new IotThingsModelValue();
        modelValue.setDeviceId(deviceId);
        modelValue.setModelId(modelId);
        modelValue.setIdentifier(mqttKey);
        modelValue.setValue(value);
        return modelValue;
    }

    // 根据不同的key返回默认值
    private String getDefaultValueForKey(String key,Long productId, String serialNumber) {
        switch (key) {
            case "mqtt_username":
                return "FastBee";
            case "mqtt_password":
                return "P"+toolService.getStringRandom(15);
            case "mqtt_secret":
                return "K"+toolService.getStringRandom(15);
            case "mqtt_clientId":
                String userId = "1";
                try {
                    LoginUser loginUser = getLoginUser();
                    SysUser sysUser = loginUser.getUser();
                    userId = sysUser.getUserId().toString();
                } catch (ServiceException e) {

                }
                return "S&" + serialNumber + "&" + productId + "&" + userId;
            case "mqtt_port":
                return brokerPort.toString();
            case "mqtt_topic":
                return "defaultTopic";
            case "mqtt_host_ip":
                return "127.0.0.1";
            case "device_status":
                return "0";
            default:
                return "";
        }
    }

    private List<IotThingsModelValueItem> convertJsonToValueList(JSONObject thingsModelObject) {
        List<IotThingsModelValueItem> valueList = new ArrayList<>();
        addValueListFromJsonArray(valueList, thingsModelObject.getJSONArray("properties"));
        addValueListFromJsonArray(valueList, thingsModelObject.getJSONArray("functions"));
        addValueListFromJsonArray(valueList, thingsModelObject.getJSONArray("events"));
        addValueListFromJsonArray(valueList, thingsModelObject.getJSONArray("relations"));
        return valueList;
    }

    private void addValueListFromJsonArray(List<IotThingsModelValueItem> valueList, JSONArray jsonArray) {
        if (!CollectionUtils.isEmpty(jsonArray)) {
            valueList.addAll(jsonArray.toJavaList(IotThingsModelValueItem.class));
        }
    }

    private void processValueItem(IotThingsModelValueItem item, Map<String, String> maps) {
        String id = item.getId();
        RedisValueItem valueItem = new RedisValueItem();
        valueItem.setValue("");
        valueItem.setId(id);
        valueItem.setName(item.getName());
        valueItem.setModelId(item.getModelId());
        if (item.getDatatype() != null) {
            DataEnum dataEnum = DataEnum.convert(item.getDatatype().getType());
            switch (dataEnum) {
                case INTEGER:
                case DECIMAL:
                case STRING:
                case FILE:
                case ENUM:
                    maps.put(id, JSONObject.toJSONString(valueItem));
                    break;
                case ARRAY:
                    processArrayValueItem(item, valueItem, maps);
                    break;
                case OBJECT:
                    processObjectValueItem(item, valueItem, maps);
                    break;
                case BOOLEAN:
                    valueItem.setValue("0");
                    maps.put(id, JSONObject.toJSONString(valueItem));
                    break;
            }
        }
    }

    private void processArrayValueItem(IotThingsModelValueItem item, RedisValueItem valueItem, Map<String, String> maps) {
        // 数组元素赋值（英文逗号分割的字符串,包含简单类型数组和对象类型数组，数组元素ID格式：array_01_humidity）
        if ("object".equals(item.getDatatype().getArrayType())) {
            for (ThingsModelJson param : item.getDatatype().getParams()) {
                processArrayParam(param, valueItem, maps, item.getDatatype().getArrayCount());
            }
        } else if ("bool".equals(item.getDatatype().getArrayType())) {
            String boolDefaultValue = String.join(",", Collections.nCopies(item.getDatatype().getArrayCount(), "0")) + "";
            valueItem.setValue(boolDefaultValue);
            maps.put(item.getId(), JSONObject.toJSONString(valueItem));
        } else {
//            String value = String.join(",", Collections.nCopies(item.getDatatype().getArrayCount(), ""));
//            // 如果生成的值全是空格，则不进行存储
//            if (!value.trim().isEmpty()) {
//                valueItem.setValue(value);
//                maps.put(item.getId(), JSONObject.toJSONString(valueItem));
//            }
            valueItem.setValue(String.join(",", Collections.nCopies(item.getDatatype().getArrayCount(), "")));
            maps.put(item.getId(), JSONObject.toJSONString(valueItem));
        }
    }

    private void processArrayParam(ThingsModelJson param, RedisValueItem valueItem, Map<String, String> maps, int arrayCount) {
        String defaultValue = String.join(",", Collections.nCopies(arrayCount, ""));
        if ("bool".equals(param.getDatatype().getType())) {
            String boolDefaultValue = String.join(",", Collections.nCopies(arrayCount, "0")) + "";
            valueItem.setValue(boolDefaultValue);
        } else {
            valueItem.setValue(defaultValue);
        }
        valueItem.setName(param.getName());
        valueItem.setId(param.getId());
        maps.put(param.getId(), JSONObject.toJSONString(valueItem));
    }

    private void processObjectValueItem(IotThingsModelValueItem item, RedisValueItem valueItem, Map<String, String> maps) {
        for (ThingsModelJson param : item.getDatatype().getParams()) {
            valueItem.setName(param.getName());
            valueItem.setId(param.getId());
            if (param.getDatatype() != null && "bool".equals(param.getDatatype().getType())) {
                valueItem.setValue("0");
            }
            maps.put(param.getId(), JSONObject.toJSONString(valueItem));
        }

    }

    /**
     * 修改设备管理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateIotDevice(IotDevice iotDevice)
    {
        // 设备编号唯一检查
        IotDevice oldDevice = iotDeviceMapper.selectDeviceByDeviceId(iotDevice.getDeviceId());
        if(oldDevice==null){
            throw new ServiceException("未查询到该设备");
        }
        if (!oldDevice.getSerialNumber().equals(iotDevice.getSerialNumber())) {
            IotDevice existDevice = iotDeviceMapper.selectDeviceBySerialNumber(iotDevice.getSerialNumber());
            if (existDevice != null) {
                return AjaxResult.error("设备编号：" + iotDevice.getSerialNumber() + " 已经存在，修改失败", 0);
            }
        }
        //物模型参数的值(IOT_Things_Model_Value)不包含遥测属性所对应的值
        List<ThingsModelJson> thingsModels = iotDevice.getThingsModels();
        List<IotThingsModelValue>  iotThingsModels = processThingsModels(thingsModels,iotDevice.getDeviceId());

        // 未激活状态,可以修改产品以及物模型值
//        if (iotDevice.getStatus() != 3) {
            // 缓存设备状态（物模型值）
            cacheDeviceStatus(iotDevice.getProductId(), oldDevice.getSerialNumber());
            String key = RedisKeyBuilder.buildTSLVCacheKey(iotDevice.getProductId(), oldDevice.getSerialNumber());

            for(IotThingsModelValue item:iotThingsModels){
                String id = item.getIdentifier();
                String itemValue = item.getValue();
                Long itemDeviceId = item.getDeviceId();
                Long type = item.getType();
                try {
                //判断关系修改
                    if(type.longValue() == 4 && id.contains("_")){
                        if(itemValue.contains(itemDeviceId.toString())){
                            return AjaxResult.error("不能添加本设备");
                        }
                        IotThingsModelValue devicesValue = new IotThingsModelValue();
                        IotThingsModel deviceModel = null;
                        // 设置设备模型信息
                        String identifier = id.split("_")[1] + "_" + id.split("_")[0];
                        deviceModel = getDeviceByIdentifier(identifier);
                        devicesValue.setIdentifier(deviceModel.getIdentifier());
                        devicesValue.setModelId(deviceModel.getModelId());
                        devicesValue.setValue(itemDeviceId.toString());

                        Long[] deviceIds = convertToLongArray(itemValue);
                        String deviceValue = devicesValue.getValue();
                        Map<String, Object> param = new HashMap<>();
                        param.put("value", deviceValue);
                        param.put("identifier", devicesValue.getIdentifier());
                        if(devicesValue!=null && deviceValue!=null) {
                            // 删除已有的父设备关系
                            iotThingsModelValueMapper.deleteParentDeviceValue(param);
                            if (deviceIds != null && deviceIds.length > 0) {
                                // 遍历每个设备ID并验证关联
                                for (Long deviceId : deviceIds) {

                                    // 验证设备是否存在指定的物模型
                                    IotDeviceShortOutput device = iotDeviceMapper.selectDeviceRunningStatusByDeviceId(deviceId);
                                    if (!isValidDeviceModel(device, devicesValue.getModelId())) {
                                        return AjaxResult.error("关联设备不存在该物模型");
                                    }

                                    // 验证是否成环
                                    if (checkForCyclicDependency(deviceId, id,itemDeviceId)) {
                                        return AjaxResult.error("关系添加成环");
                                    }

                                    devicesValue.setDeviceId(deviceId);
                                    param.put("deviceId", deviceId);
                                    IotThingsModelValue iotThingsModelValue = iotThingsModelValueMapper.selectSubDeviceByDeviceId(param);
                                    String value = (iotThingsModelValue!=null) ? iotThingsModelValue.getValue() : "";

                                    if(StringUtils.isBlank(value)||value.equals(deviceValue)){
                                        iotThingsModelValueMapper.insertMqttValue(devicesValue);
                                    }else if(!value.contains(deviceValue)){
                                        value = insertIotValue(value,deviceValue);
                                        devicesValue.setValue(value);
                                        iotThingsModelValueMapper.updateMqttValue(devicesValue);
                                    }
                                }
                            }
                        }
                        // 处理已存在的设备关系
                        List<IotThingsModelValue> iotThingsModel = iotThingsModelValueMapper.selectSubDeviceByValue(param);
                        for (IotThingsModelValue iotThingsModelValue : iotThingsModel) {
                            Long deviceId = iotThingsModelValue.getDeviceId();
                            if(!Arrays.asList(deviceIds).contains(deviceId)){
                                String iotValue = iotThingsModelValue.getValue();
                                String value = deleteIotValue(iotValue,deviceValue);
                                if(StringUtils.isNotBlank(value)){
                                    devicesValue.setValue(value);
                                    devicesValue.setDeviceId(deviceId);
                                    iotThingsModelValueMapper.updateMqttValue(devicesValue);
                                }
                            }
                        }
                    }

                    String cacheValue = redisCache.getCacheMapValue(key, id);
                    RedisValueItem valueItem = JSON.parseObject(cacheValue, RedisValueItem.class);
                    valueItem.setValue(itemValue);
                    valueItem.setId(id);
                    valueItem.setName(item.getModelName());
                    redisCache.setCacheMapValue(key,id,JSONObject.toJSONString(valueItem));
                } catch (Exception e) {
                    e.printStackTrace();
                    return AjaxResult.error(e.getMessage());
                }
            }
            if (!oldDevice.getSerialNumber().equals(iotDevice.getSerialNumber())) {
                String newkey = RedisKeyBuilder.buildTSLVCacheKey(iotDevice.getProductId(), iotDevice.getSerialNumber());
                redisCache.hashPutAll(newkey, redisCache.hashEntity(key));
                redisCache.deleteObject(key);
            }

//        }
//        if(!iotThingsModels.isEmpty()&&iotDevice.getStatus() != 3){
        if(!iotThingsModels.isEmpty()){
            iotThingsModelValueMapper.deleteThingsModelValueByDeviceId(iotDevice.getDeviceId());
            iotThingsModelValueMapper.insertThingsModelValue(iotThingsModels);
        }
        SysUser user = getLoginUser().getUser();
        iotDevice.setUpdateBy(user.getUserName());
        iotDevice.setUpdateTime(DateUtils.getNowDate());
        iotDeviceMapper.updateIotDevice(iotDevice);
        // 更新前设备状态为禁用，启用后状态默认为离线，满足时下发获取设备最新状态指令
//        if (oldDevice.getStatus() == 2 && iotDevice.getStatus() == 4) {
//            // 发布设备信息，设备收到该消息后上报最新状态
//            // emqxService.publishInfo(oldDevice.getProductId(), oldDevice.getSerialNumber());
//        }
        return AjaxResult.success("修改成功", 1);
    }

    // 验证设备是否存在指定物模型
    private boolean isValidDeviceModel(IotDeviceShortOutput device, Long modelId) {
        Map<String, Object> param = new HashMap<>();
        param.put("productId", device.getProductId());
        param.put("modelId", modelId);
        return iotProductMapper.thingsModelsIsExist(param) > 0;
    }

    private boolean checkForCyclicDependency(Long deviceId, String identifier, Long itemDeviceId) {
        Set<Long> visitedDevices = new HashSet<>();  // 用于记录已访问过的设备ID
        return checkForCyclicDependencyRecursive(deviceId, identifier, itemDeviceId, visitedDevices);
    }

    // 递归检查成环
    private boolean checkForCyclicDependencyRecursive(Long deviceId, String identifier, Long itemDeviceId, Set<Long> visitedDevices) {
        // 如果设备已经被访问过，表示成环，直接返回
        if (visitedDevices.contains(deviceId)) {
            return true;
        }
        // 将当前设备ID添加到已访问设备列表
        visitedDevices.add(deviceId);

        // 获取设备对应的模型值
        Map<String, Object> param = new HashMap<>();
        param.put("deviceId", deviceId);
        param.put("identifier", identifier);
        IotThingsModelValue thingsModelValue = iotThingsModelValueMapper.selectSubDeviceByDeviceId(param);

        // 如果没有找到模型值，直接返回false
        if (thingsModelValue == null) {
            return false;
        }

        String value = thingsModelValue != null ? thingsModelValue.getValue() : "";
        Long[] deviceIds = convertToLongArray(value);

        // 如果关联的设备ID不为空，继续递归检查
        if (deviceIds != null && deviceIds.length > 0) {
            for (Long deviceId1 : deviceIds) {
                // 检查当前设备的子设备是否包含目标设备
                if (deviceId1.equals(itemDeviceId)) {
                    return true;
                }
                // 递归检查子设备
                if (checkForCyclicDependencyRecursive(deviceId1, identifier, itemDeviceId, visitedDevices)) {
                    return true;
                }
            }
        }
        return false;
    }

    private void setIotThingsModel(IotThingsModelValue iotThingsModel, ThingsModelJson things,Long deviceId) {
        iotThingsModel.setDeviceId(deviceId);
        iotThingsModel.setValue(things.getValue());
        iotThingsModel.setModelId(things.getModelId());
        iotThingsModel.setIdentifier(things.getId());
        iotThingsModel.setModelName(things.getName());
        iotThingsModel.setType((long)things.getType());
        iotThingsModel.setSubType((long)things.getSubType());
    }

    public List<IotThingsModelValue> processThingsModels(List<ThingsModelJson> thingsModels,Long deviceId) {
        List<IotThingsModelValue> iotThingsModels = new ArrayList<>();
        for (ThingsModelJson thingsModelJson : thingsModels) {
            Long modeId = thingsModelJson.getModelId();
            if ("object".equals(thingsModelJson.getDatatype().getType())) {
                List<ThingsModelJson> thingsList = thingsModelJson.getDatatype().getParams();
                for (ThingsModelJson things : thingsList) {
                    if (things.getValue() != null && !things.getValue().trim().isEmpty()) {
                        IotThingsModelValue iotThingsModel = new IotThingsModelValue();
                        things.setModelId(modeId);
                        setIotThingsModel(iotThingsModel, things,deviceId);
                        iotThingsModels.add(iotThingsModel);
                    }
                }
            }// 数组类型物模型里面对象赋值
            else if ("object".equals(thingsModelJson.getDatatype().getArrayType())) {
                List<ThingsModelJson>[] thingsLists = thingsModelJson.getDatatype().getArrayParams();
                Map<String, List<String>> valueMap = new HashMap<>();
                for (int i = 0; i < thingsModelJson.getDatatype().getArrayCount(); i++) {
                    for (ThingsModelJson things : thingsLists[i]) {
                        String id = things.getId();
                        String value = things.getValue();
                        if (id.startsWith("array_")) {
                            String baseId = id.substring(9);
                            value = (value == null || value.trim().isEmpty()) ? "" : value.trim();
                            valueMap.putIfAbsent(baseId, new ArrayList<>());
                            valueMap.get(baseId).add(value.trim());
                        }
                    }
                }
                for (Map.Entry<String, List<String>> entry : valueMap.entrySet()) {
                    String id = entry.getKey();
                    String joinedValues = String.join(",", entry.getValue());
                    thingsModelJson.setId(id);
                    thingsModelJson.setValue(joinedValues);
                    IotThingsModelValue iotThingsModel = new IotThingsModelValue();
                    setIotThingsModel(iotThingsModel, thingsModelJson,deviceId);
                    iotThingsModels.add(iotThingsModel);
                }

            }else if (thingsModelJson.getValue() != null && !thingsModelJson.getValue().isEmpty()) {
                IotThingsModelValue iotThingsModel = new IotThingsModelValue();
                setIotThingsModel(iotThingsModel, thingsModelJson,deviceId);
                iotThingsModels.add(iotThingsModel);
            }
        }
        return iotThingsModels.stream()
                .collect(Collectors.toMap(
                        item -> item.getDeviceId() + ":" + item.getIdentifier(),
                        item -> item,
                        (existing, replacement) -> existing))
                .values()
                .stream()
                .collect(Collectors.toList());
    }
    /**
     * 批量删除设备管理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteIotDeviceByDeviceIds(Long deviceId)  throws SchedulerException
    {
        IotDevice device = iotDeviceMapper.selectDeviceByDeviceId(deviceId);
        String serialNumber = device.getSerialNumber();
        // 设备下不能有场景联动
//        List<SceneDeviceBindVO> sceneDeviceBindVOList = sceneDeviceMapper.listSceneDeviceBind(serialNumber);
//        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(sceneDeviceBindVOList)) {
//            String sceneNames = sceneDeviceBindVOList.stream().map(SceneDeviceBindVO::getSceneName).collect(Collectors.joining("，"));
//            return AjaxResult.error("删除失败，请先修改或删除对应设备下的场景联动：" + sceneNames);
//        }

        // 删除分组中的设备
        iotDeviceMapper.deleteDeviceGroupByDeviceId(deviceId);
        // 删除设备用户信息。
        iotDeviceUserMapper.deleteDeviceUserByDeviceId(deviceId);
        // 删除定时任务
        // iotDeviceJobService.deleteJobByDeviceIds(deviceId);
        // 批量删除设备监测日志tg库
        logService.deleteDeviceLogByDeviceNumber(serialNumber);
        // 批量删除设备事件日志
        eventLogService.deleteEventLogByDeviceNumber(serialNumber);
        // 批量删除设备功能日志
        functionLogService.deleteFunctionLogByDeviceNumber(serialNumber);
        // 批量删除设备模拟日志
        simulateLogService.deleteSimulateLogByDeviceNumber(serialNumber);
        // 批量删除设备告警记录
        alertLogService.deleteAlertLogBySerialNumber(serialNumber);
        // redis中删除设备物模型（状态）
        String key = RedisKeyBuilder.buildTSLVCacheKey(device.getProductId(), serialNumber);
        redisCache.deleteObject(key);

        //删除对应关系物模型值
        List<IotThingsModelValue> iotThingsModel = iotThingsModelValueMapper.selectThingsModelValue(deviceId);
        for (IotThingsModelValue iotThingsModelValue : iotThingsModel) {
            String id = iotThingsModelValue.getIdentifier();

            if(iotThingsModelValue.getType().longValue() == 4 && id.contains("_")){
                IotThingsModelValue devicesValue = new IotThingsModelValue();
                String identifier = id.split("_")[1] + "_" + id.split("_")[0];
                if (identifier != null) {
                    Map<String, Object> param = new HashMap<>();
                    param.put("value", deviceId.toString());
                    param.put("identifier", identifier);
                    //根据物模型值查询物模型
                    List<IotThingsModelValue> iotThingsModelByValue = iotThingsModelValueMapper.selectSubDeviceByValue(param);
                    for (IotThingsModelValue thingsModelValue : iotThingsModelByValue) {
                        String iotValue = thingsModelValue.getValue();
                        String value = deleteIotValue(iotValue, deviceId.toString());
                        devicesValue.setValue(value);
                        devicesValue.setDeviceId(thingsModelValue.getDeviceId());
                        devicesValue.setIdentifier(identifier);
                        if (value.isEmpty() || value==null) {
                            param.put("deviceId", thingsModelValue.getDeviceId());
                            iotThingsModelValueMapper.deleteSubDevicesValue(param);
                        } else {
                            iotThingsModelValueMapper.updateMqttValue(devicesValue);
                        }
                    }
                }
            }
        }

        //删除设备物模型
        iotThingsModelValueMapper.deleteThingsModelValueByDeviceId(deviceId);
        // 删除设备
        iotDeviceMapper.deleteIotDeviceByDeviceId(deviceId);
        //查询是否有子设备
//        Integer subDeviceCount = this.getSubDeviceCount(serialNumber);
//        if (null != subDeviceCount && subDeviceCount > 0) {
//            this.deleteSubDevice(serialNumber);
//        }
        return 1;
    }

    /**
     * 删除设备管理信息
     */
    @Override
    public int deleteIotDeviceByDeviceId(Long deviceId)
    {
        return iotDeviceMapper.deleteIotDeviceByDeviceId(deviceId);
    }

    /**
     * 查询分组可添加设备分页列表
     */
    @Override
    public List<IotDevice> selectDeviceListByGroup(IotDevice device) {
        return iotDeviceMapper.selectIotDeviceList(device);
    }

    /**
     * 查询设备分页简短列表
     */
    @Override
    public List<IotDeviceShortOutput> selectDeviceShortList(IotDevice iotDevice) {
        //        List<IotDeviceShortOutput> deviceList = new ArrayList<>();
//        if (groupId!=null){
//            Long[] groupIds = iotDeviceGroupServiceImpl.getAllSubGroupIds(groupId);
//            groupIds = Arrays.copyOf(groupIds, groupIds.length + 1);
//            groupIds[groupIds.length - 1] = groupId;
//            for(Long id : groupIds){
//                iotDevice.setGroupId(id);
//                List<IotDeviceShortOutput> devices = iotDeviceMapper.selectDeviceShortList(iotDevice);
//                if (devices != null) {
//                    deviceList.addAll(devices);
//                }
//            }
//            deviceList = deviceList.stream()
//                    .distinct()
//                    .collect(Collectors.toList());
//        }else{
//            deviceList = iotDeviceMapper.selectDeviceShortList(iotDevice);
//        }
//        return deviceList;
        List<IotDeviceShortOutput> deviceList = iotDeviceMapper.selectDeviceShortList(iotDevice);
        for(IotDeviceShortOutput device : deviceList){
            Long deviceId = device.getDeviceId();
            //所属单位（分组名）
            List<IotDeviceGroup> iotDeviceGroup = iotDeviceGroupMapper.selectGroupByDeviceId(deviceId);
            List<String> groupNames = iotDeviceGroup.stream()
                    .map(IotDeviceGroup::getGroupName)
                    .distinct()
                    .collect(Collectors.toList());
            device.setGroupName(groupNames);
            //所属名（从属关系）
            String thingsModelValue = iotThingsModelValueMapper.selectDeviceNameByDeviceId(deviceId);
            if(thingsModelValue!=null){
                device.setDeviceBelongName(thingsModelValue);
            }

        }
        return deviceList;
    }

    /**
     * 生成设备唯一编号
     */
    @Override
    public String generationDeviceNum(Integer type) {
        // 设备编号：D + userId + 15位随机字母和数字
        SysUser user = getLoginUser().getUser();
        String number;

        //Hex随机字符串
        if (type == 3){
            number = toolService.generateRandomHex(12);
        }else {
            number = "D" + user.getUserId().toString() + toolService.getStringRandom(10);
        }
        int count = iotDeviceMapper.getDeviceNumCount(number);
        if (count == 0) {
            return number;
        } else {
            generationDeviceNum(type);
        }
        return "";
    }

    /**
     * 根据产品id查询模板
     */
    @Override
    public IotProductmodel selectDeviceTemplateByProduct(Long productId) {
        return iotDeviceModelMapper.selectDeviceTemplateByProduct(productId);
    }

    /**
     * 查询设备
     */
    @Override
    public IotDeviceShortOutput selectDeviceRunningStatusByDeviceId(Long deviceId)  throws ServiceException {
        IotDeviceShortOutput device = iotDeviceMapper.selectDeviceRunningStatusByDeviceId(deviceId);
        if(device==null){
            throw new ServiceException("未查询到该设备");
        }
        JSONObject thingsModelObject = JSONObject.parseObject(iotThingsModelService.getCacheThingsModelByProductId(device.getProductId()));
        List<ThingsModelJson> thingsList = convertJsonToThingsList(thingsModelObject, device.getProductId(), device.getSerialNumber());
        device.setThingsModels(thingsList);
        return device;
    }

    private List<ThingsModelJson> convertJsonToThingsList(JSONObject thingsModelObject, Long productId, String serialNumber) {
        List<IotThingsModelValueItem> thingsModelValueItems = getCacheDeviceStatus(productId, serialNumber);
        List<ThingsModelJson> thingsList = new ArrayList<>();
        addThingsListFromJsonArray(thingsList, thingsModelObject.getJSONArray("properties"), thingsModelValueItems, 1);
        addThingsListFromJsonArray(thingsList, thingsModelObject.getJSONArray("functions"), thingsModelValueItems, 2);
        addThingsListFromJsonArray(thingsList, thingsModelObject.getJSONArray("events"), thingsModelValueItems, 3);
        addThingsListFromJsonArray(thingsList, thingsModelObject.getJSONArray("relations"), thingsModelValueItems, 4);
        return thingsList;
    }

    private void addThingsListFromJsonArray(List<ThingsModelJson> thingsList, JSONArray jsonArray, List<IotThingsModelValueItem> thingsModelValues, Integer type) {
        if (!CollectionUtils.isEmpty(jsonArray)) {
            thingsList.addAll(convertJsonToThingsList(jsonArray, thingsModelValues, type));
        }
    }

    /**
     * 物模型基本类型转换赋值
     */
    @Async
    public List<ThingsModelJson> convertJsonToThingsList(JSONArray jsonArray, List<IotThingsModelValueItem> thingsModelValues, Integer type) {
        List<ThingsModelJson> thingsModelList = new ArrayList<>();
        // 遍历 JSON 数组并处理每个 thingsModel
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject thingsJson = jsonArray.getJSONObject(i);
            ThingsModelJson thingsModel = buildThingsModel(thingsJson, thingsModelValues, type);
            // 处理 enum 类型的 value
            if (JsonUtils.isJson(thingsModel.getValue())) {
                handleEnumValue(thingsModel, thingsModelList);
            } else {
                // 物模型项添加到集合
                thingsModelList.add(thingsModel);
            }
        }
        return thingsModelList;
    }

    private ThingsModelJson buildThingsModel(JSONObject thingsJson, List<IotThingsModelValueItem> thingsModelValues, Integer type) {
        ThingsModelJson thingsModel = new ThingsModelJson();

        // 基础属性赋值
        thingsModel.setId(thingsJson.getString("id"));
        thingsModel.setName(thingsJson.getString("name"));
        thingsModel.setIsMonitor(getNullableInt(thingsJson, "isMonitor"));
        thingsModel.setIsReadonly(getNullableInt(thingsJson, "isReadonly"));
        thingsModel.setIsChart(getNullableInt(thingsJson, "isChart"));
        thingsModel.setIsHistory(getNullableInt(thingsJson, "isHistory"));
        thingsModel.setIsRequired(getNullableInt(thingsJson, "isRequired"));
        thingsModel.setIsUnique(getNullableInt(thingsJson, "isUnique"));
        thingsModel.setOrder(getNullableInt(thingsJson, "order"));
        thingsModel.setSubType(getNullableInt(thingsJson, "subType"));
        Integer modelId = thingsJson.getInteger("modelId");
        if (modelId != null) {
            thingsModel.setModelId(modelId.longValue());
        }
        thingsModel.setType(type);

        // 获取value
        for (IotThingsModelValueItem valueItem : thingsModelValues) {
            if (valueItem.getId().equals(thingsModel.getId())) {
                thingsModel.setValue(valueItem.getValue());
                thingsModel.setTs(valueItem.getTs());
                break;
            }
        }

        // 解析并设置数据类型
        JSONObject datatypeJson = thingsJson.getJSONObject("datatype");
        // json转DataType(DataType赋值)
        Datatypes dataType = convertJsonToDataType(datatypeJson, thingsModelValues, type, thingsModel.getId() + "_");
        thingsModel.setDatatype(dataType);
        return thingsModel;
    }

    private int getNullableInt(JSONObject json, String key) {
        return Optional.ofNullable(json.getInteger(key)).orElse(0);
    }

    private void handleEnumValue(ThingsModelJson thingsModel, List<ThingsModelJson> thingsModelList) {
        JSONObject jsonObject = JSONObject.parseObject(thingsModel.getValue());
        Datatypes dataType = thingsModel.getDatatype();
        for (EnumItems enumItem : dataType.getEnumList()) {
            ThingsModelJson model = new ThingsModelJson();
            BeanUtils.copyProperties(thingsModel, model);
            String val = jsonObject.getString(enumItem.getValue());
            model.setValue(val);
            model.setName(enumItem.getValue());
            thingsModelList.add(model);
        }
    }

    /**
     * 物模型DataType转换
     */
    private Datatypes convertJsonToDataType(JSONObject datatypeJson, List<IotThingsModelValueItem> thingsModelValues, Integer type, String parentIdentifier) {
        Datatypes dataType = new Datatypes();
        //有些物模型数据定义为空的情况兼容
        if (datatypeJson == null) {
            return dataType;
        }
        dataType.setType(datatypeJson.getString("type"));
        switch (dataType.getType()) {
            case "decimal":
            case "integer":
                setNumericDataType(dataType, datatypeJson);
                break;
            case "bool":
                setBooleanDataType(dataType, datatypeJson);
                break;
            case "string":
                dataType.setMaxLength(datatypeJson.getInteger("maxLength"));
                break;
            case "enum":
                setEnumDataType(dataType, datatypeJson);
                break;
            case "object":
                setObjectDataType(dataType, datatypeJson, thingsModelValues, type, parentIdentifier);
                break;
            case "array":
                setArrayDataType(dataType, datatypeJson, thingsModelValues, type, parentIdentifier);
                break;
        }
        return dataType;
    }
    //integer,decimal
    private void setNumericDataType(Datatypes dataType, JSONObject datatypeJson) {
        dataType.setMax(datatypeJson.getBigDecimal("max"));
        dataType.setMin(datatypeJson.getBigDecimal("min"));
        dataType.setStep(datatypeJson.getBigDecimal("step"));
        dataType.setUnit(datatypeJson.getString("unit"));
    }
    //bool
    private void setBooleanDataType(Datatypes dataType, JSONObject datatypeJson) {
        dataType.setFalseText(datatypeJson.getString("falseText"));
        dataType.setTrueText(datatypeJson.getString("trueText"));
    }
    //enum
    private void setEnumDataType(Datatypes dataType, JSONObject datatypeJson) {
        List<EnumItems> enumItemList = JSON.parseArray(datatypeJson.getString("enumList"), EnumItems.class);
        dataType.setEnumList(enumItemList);
        dataType.setShowWay(datatypeJson.getString("showWay"));
    }
    //object
    private void setObjectDataType(Datatypes dataType, JSONObject datatypeJson, List<IotThingsModelValueItem> thingsModelValues, Integer type, String parentIdentifier) {
        JSONArray jsonArray = JSON.parseArray(datatypeJson.getString("params"));
        // 物模型值过滤（parentId_开头）
        thingsModelValues = thingsModelValues.stream().filter(x -> x.getId().startsWith(parentIdentifier)).collect(Collectors.toList());
        List<ThingsModelJson> thingsList = convertJsonToThingsList(jsonArray, thingsModelValues, type);
        // 排序
        thingsList = thingsList.stream().sorted(Comparator.comparing(ThingsModelJson::getOrder).reversed()).collect(Collectors.toList());
        dataType.setParams(thingsList);
    }
    //array
    private void setArrayDataType(Datatypes dataType, JSONObject datatypeJson, List<IotThingsModelValueItem> thingsModelValues, Integer type, String parentIdentifier) {
        dataType.setArrayType(datatypeJson.getString("arrayType"));
        dataType.setArrayCount(datatypeJson.getInteger("arrayCount"));
        if ("object".equals(dataType.getArrayType())) {
            // 对象数组
            JSONArray jsonArray = datatypeJson.getJSONArray("params");
            // 物模型值过滤（parentId_开头）
            thingsModelValues = thingsModelValues.stream().filter(x -> x.getId().startsWith(parentIdentifier)).collect(Collectors.toList());
            List<ThingsModelJson> thingsList = convertJsonToThingsList(jsonArray, thingsModelValues, type);
            // 排序
            thingsList = thingsList.stream().sorted(Comparator.comparing(ThingsModelJson::getOrder).reversed()).collect(Collectors.toList());
            // 数组类型物模型里面对象赋值
            List<ThingsModelJson>[] arrayParams = new List[dataType.getArrayCount()];
            for (int i = 0; i < dataType.getArrayCount(); i++) {
                List<ThingsModelJson> thingsModels = new ArrayList<>();
                for (ThingsModelJson thingsModelJson : thingsList) {
                    ThingsModelJson thingsModel = new ThingsModelJson();
                    BeanUtils.copyProperties(thingsModelJson, thingsModel);
                    String value = thingsModelJson.getValue();
                    if (StringUtils.isNotEmpty(value) && !value.equals("")) {
                        String[] values = value.split(",");
                        thingsModel.setValue(i + 1 > values.length ? " " : values[i]);
                    }
                    thingsModels.add(thingsModel);
                }
                arrayParams[i] = thingsModels;
            }
            dataType.setArrayParams(arrayParams);
        }
    }

    /**
     * 获取设备MQTT连接参数
     */
    @Override
    public DeviceMqttConnectVO getMqttConnectData(Long deviceId)  throws ServiceException {
        List<IotThingsModelValue> list=iotThingsModelValueMapper.selectDeviceMqttConnect(deviceId);
        if (list == null || list.isEmpty()) {
            throw new ServiceException("获取设备MQTT连接参数失败");
        }
        DeviceMqttConnectVO connectVO = new DeviceMqttConnectVO();
        for(IotThingsModelValue modelValue:list){
            String identifier = modelValue.getIdentifier();
            String value = modelValue.getValue();
            Map<String, Consumer<String>> setterMap = new HashMap<>();
            setterMap.put("port", v -> connectVO.setPort(Long.valueOf(v)));
            setterMap.put("password", v -> connectVO.setPasswd(v));
            setterMap.put("username", v -> connectVO.setUsername(v));
            setterMap.put("clientId", v -> connectVO.setClientId(v));
            setterMap.put("secret", v -> connectVO.setMqttsecret(v));

            for (Map.Entry<String, Consumer<String>> entry : setterMap.entrySet()) {
                if (identifier.contains(entry.getKey())) {
                    entry.getValue().accept(value);
                }
            }
        }
        if (connectVO.getPort() == null || connectVO.getUsername() == null || connectVO.getClientId() == null || connectVO.getPasswd() == null) {
            throw new ServiceException("设备MQTT连接参数不完整");
        }
        // 组装返回结果
        connectVO.setClientId(connectVO.getClientId()).setUsername(connectVO.getUsername()).setPasswd(connectVO.getPasswd()).setPort(connectVO.getPort());
        return connectVO;
    }

    /**
     * 根据设备编号查询设备
     */
    @Override
    public IotDevice selectDeviceBySerialNumber(String serialNumber) {
        IotDevice device = iotDeviceMapper.selectDeviceBySerialNumber(serialNumber);
        if (device != null) {
            // redis中获取设备状态（物模型值）
            List<IotThingsModelValueItem> list = getCacheDeviceStatus(device.getProductId(), device.getSerialNumber());
            if (list != null && list.size() > 0) {
                device.setThingsModelValue(JSONObject.toJSONString(list));
            }
        }
        return device;
    }

    /**
     * 设备状态和定位更新
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDeviceStatusAndLocation(IotDevice device) {
        // 设置自动定位和状态
        if (device.getActiveTime() == null) {
            device.setActiveTime(DateUtils.getNowDate());
        }
        int result = iotDeviceMapper.updateDeviceStatus(device);
        // 添加到设备日志
        EventLog event = new EventLog();
        event.setDeviceId(device.getDeviceId());
        event.setDeviceName(device.getDeviceName());
        event.setSerialNumber(device.getSerialNumber());
        event.setIsMonitor(0);
        try {
            LoginUser loginUser = getLoginUser();
            SysUser sysUser = loginUser.getUser();
            event.setUserId(sysUser.getUserId());
            event.setUserName(sysUser.getUserName());
        } catch (ServiceException e) {

        }

        event.setCreateTime(DateUtils.getNowDate());
        // 日志模式 1=影子模式，2=在线模式，3=其他
        event.setMode(3);
        if (device.getStatus() == 3) {
            event.setLogValue("1");
            event.setRemark("设备上线");
            event.setIdentity("online");
            event.setLogType(5);
        } else if (device.getStatus() == 4) {
            event.setLogValue("0");
            event.setRemark("设备离线");
            event.setIdentity("offline");
            event.setLogType(6);
        }
        eventLogMapper.insertEventLog(event);
        return result;
    }

    /**
     * 重置设备状态
     */
    @Override
    public int resetDeviceStatus(String deviceNum) {
        int result = iotDeviceMapper.resetDeviceStatus(deviceNum);
        return result;
    }

    /**
     * 重置设备状态
     */
    @Override
    public void reSetDeviceStatus(){
        iotDeviceMapper.reSetDeviceStatus();
    }

    /**
     * 根据设备编号查询设备信息 -不带缓存物模型值
     */
    @Override
    public IotDevice selectDeviceNoModel(String serialNumber) {
        return iotDeviceMapper.selectDeviceBySerialNumber(serialNumber);
    }

    /**
     * 设备状态
     */
    @Override
    public int updateDeviceStatus(IotDevice device) {
        return iotDeviceMapper.updateDeviceStatus(device);
    }

    /**
     * 更新设备的物模型
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ThingsModelSimpleItem> reportDeviceThingsModelValue(ThingsModelValuesInput input, int type) {
        String key = RedisKeyBuilder.buildTSLVCacheKey(input.getProductId(), input.getDeviceNumber());
        Map<String, String> maps = new HashMap<String, String>();
        List<ThingsModelSimpleItem> list = new ArrayList<>();

        //属性存储集合
        List<DeviceLog> deviceLogList = new ArrayList<>();
        //指令存储集合
        List<FunctionLog> functionLogList = new ArrayList<>();
        //事件存储集合
        List<EventLog> eventLogList = new ArrayList<>();
        for (ThingsModelSimpleItem item : input.getThingsModelValueRemarkItem()) {
            String identity = item.getId();
            //Integer slaveId = input.getSlaveId() == null ? item.getSlaveId() : input.getSlaveId();
            String serialNumber = input.getDeviceNumber();
            if (identity.startsWith("array_")) {
                identity = identity.substring(9);
            }
            // 查询redis中物模型
            identity = identity;
            if(identity.equals("status")){
                list.add(item);
                continue;
            }
            IotPropertyDto dto = iotThingsModelService.getSingleThingModels(input.getProductId(), identity);
            if (null == dto) {
                continue;
            }
            String id = item.getId();
            String value = item.getValue();

            /* ★★★★★★★★★★★★★★★★★★★★★★  数据计算 -开始 ★★★★★★★★★★★★★★★★★★★★★★*/
            //有计算公式的，经过计算公式
            if (dto.getFormula() != null && !"".equals(dto.getFormula())) {
                Map<String, String> params = new HashMap<>();
                params.put("%s", value);
                value = String.valueOf(CaculateUtils.execute(dto.getFormula(), params));
                item.setValue(value);
            }
            /* ★★★★★★★★★★★★★★★★★★★★★★  数据计算 -结束  ★★★★★★★★★★★★★★★★★★★★★★*/

            /* ★★★★★★★★★★★★★★★★★★★★★★  处理数据 - 开始 ★★★★★★★★★★★★★★★★★★★★★★*/
            String cacheValue = redisCache.getCacheMapValue(key, identity);
            if (cacheValue == null || cacheValue.isEmpty()) {
                continue;
            }
            RedisValueItem valueItem = JSON.parseObject(cacheValue, RedisValueItem.class);

            if (id.startsWith("array_")) {
                // 查询是否有缓存，如果没有先进行缓存
                if (!redisCache.containsKey(key)) {
                    IotDevice device = this.selectDeviceBySerialNumber(input.getDeviceNumber());
                    this.selectIotDeviceByDeviceId(device.getDeviceId());
                }
                int index = Integer.parseInt(id.substring(6, 8));
                // 设置值，获取数组值，然后替换其中元素
                valueItem.setTs(DateUtils.getNowDate());
                String[] values = valueItem.getValue().split(",");
                values[index] = value;
                valueItem.setValue(String.join(",", values));

            redisCache.setCacheMapValue(key,identity, JSONObject.toJSONString(valueItem));
            //maps.put(identity, JSONObject.toJSONString(valueItem));
            } else {
                valueItem.setValue(value);
                valueItem.setTs(DateUtils.getNowDate());
                maps.put(identity, JSONObject.toJSONString(valueItem));
            }
            /* ★★★★★★★★★★★★★★★★★★★★★★  处理数据 - 结束 ★★★★★★★★★★★★★★★★★★★★★★*/

            /*★★★★★★★★★★★★★★★★★★★★★★  存储数据 - 开始 ★★★★★★★★★★★★★★★★★★★★★★*/
            if (null != dto.getIsHistory()) {
                ThingsModelType modelType = ThingsModelType.getType(dto.getType());
                switch (modelType) {
                    case PROP:
                        if (1 == dto.getIsHistory()) {
                            DeviceLog deviceLog = new DeviceLog();
                            deviceLog.setSerialNumber(serialNumber);
                            deviceLog.setLogType(type);
                            // 1=影子模式，2=在线模式，3=其他
                            deviceLog.setMode(2);
                            // 设备日志值
                            deviceLog.setLogValue(value);
                            deviceLog.setRemark(item.getRemark());
                            deviceLog.setIdentity(id);
                            deviceLog.setModelName(dto.getName());
                            deviceLog.setCreateTime(DateUtils.getNowDate());
                            deviceLog.setIsMonitor(dto.getIsMonitor());
                            deviceLogList.add(deviceLog);
                        }
                        break;
                    case SERVICE:
                        if (1 == dto.getIsHistory()) {
                            FunctionLog function = new FunctionLog();
                            function.setCreateTime(DateUtils.getNowDate());
                            function.setFunValue(value);
                            function.setSerialNumber(input.getDeviceNumber());
                            function.setIdentify(id);
                            function.setShowValue(value);
                            // 属性获取
                            function.setFunType(2);
                            function.setModelName(dto.getName());
                            functionLogList.add(function);
                        }
                        break;
                    case EVENT:
                        IotDevice device = this.selectDeviceBySerialNumber(input.getDeviceNumber());
                        EventLog event = new EventLog();
                        event.setDeviceId(device.getDeviceId());
                        event.setDeviceName(device.getDeviceName());
                        event.setLogValue(value);
                        event.setSerialNumber(input.getDeviceNumber().contains("_") ? device.getSerialNumber() : input.getDeviceNumber());
                        event.setIdentity(id);
                        event.setLogType(3);
                        event.setIsMonitor(0);
//                        event.setUserId(device.getUserId());
//                        event.setUserName(device.getUserName());
                        event.setCreateTime(DateUtils.getNowDate());
                        // 1=影子模式，2=在线模式，3=其他
                        event.setMode(2);
                        eventLogList.add(event);
                        break;
                }
            }
            list.add(item);
        }
        // 缓存最新一条数据到redis
        redisCache.hashPutAll(key, maps);
        //存储历史数据
        if (!CollectionUtils.isEmpty(deviceLogList)) {
            for (DeviceLog deviceLog : deviceLogList) {
                logService.saveDeviceLog(deviceLog);
            }
        }
        //指令存储,影子模式不存储
        if (!CollectionUtils.isEmpty(functionLogList)) {
            functionLogService.insertBatch(functionLogList);
        }
        if (!CollectionUtils.isEmpty(eventLogList)) {
            //事件存储
            eventLogService.insertBatch(eventLogList);
        }
        /* ★★★★★★★★★★★★★★★★★★★★★★  存储数据 - 结束 ★★★★★★★★★★★★★★★★★★★★★★*/
        return list;
    }

    /**
     * 更新设备井口的信息
     * 更新数据库，更新缓存，设备更改状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reportDeviceInfo(DeviceReport reportMessage) {
        String serialNumber = reportMessage.getSerialNumber();
        String key = RedisKeyBuilder.buildDeviceInfoCacheKey(reportMessage.getProductId(), serialNumber);

        Map<String, String> maps = new HashMap<String, String>();
        //属性存储集合
        List<DeviceLog> deviceLogList = new ArrayList<>();
        /*★★★★★★★★★★★★★★★★★★★★★★  存储数据 - 开始 ★★★★★★★★★★★★★★★★★★★★★★*/
        for (ThingsModelSimpleItem item : reportMessage.getValuesInput().getThingsModelValueRemarkItem()) {
            String id = item.getId();
            String value = item.getValue();
            DeviceLog deviceLog = new DeviceLog();
            deviceLog.setSerialNumber(serialNumber);
            deviceLog.setLogType(1);
            // 1=影子模式，2=在线模式，3=其他
            deviceLog.setMode(2);
            // 设备日志值
            deviceLog.setLogValue(value);
            deviceLog.setRemark(item.getRemark());
            deviceLog.setIdentity(id);
            deviceLog.setModelName(id);
            deviceLog.setCreateTime(DateUtils.getNowDate());
            deviceLog.setIsMonitor(1);
            deviceLogList.add(deviceLog);
            maps.put(id, value);
            continue;
        }
        // 缓存最新一条数据到redis
        redisCache.hashPutAll(key, maps);
        //存储历史数据
        if (!CollectionUtils.isEmpty(deviceLogList)) {
            for (DeviceLog deviceLog : deviceLogList) {
                logService.saveDeviceLog(deviceLog);
            }
        }
        /* ★★★★★★★★★★★★★★★★★★★★★★  存储数据 - 结束 ★★★★★★★★★★★★★★★★★★★★★★*/
        return;
    }

    /**
     * 上报设备信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int reportDevice(IotDevice device, IotDevice deviceEntity) {
        int result = 0;
        if (deviceEntity != null) {
            device.setUpdateTime(DateUtils.getNowDate());
            if (deviceEntity.getActiveTime() == null || deviceEntity.getActiveTime().equals("")) {
                device.setActiveTime(DateUtils.getNowDate());
            }
            // 不更新物模型
            device.setThingsModelValue(null);
            result = iotDeviceMapper.updateDeviceStatus(device);
        }
        return result;
    }

    /**
     * 设备认证后自动添加设备
     *
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertDeviceAuto(String serialNumber, Long userId, Long productId) {
        // 设备编号唯一检查
        int count = iotDeviceMapper.getDeviceNumCount(serialNumber);
        if (count != 0) {
            log.error("设备编号：" + serialNumber + "已经存在了，新增设备失败");
            return 0;
        }
        IotDevice device = new IotDevice();
        device.setSerialNumber(serialNumber);

        // 设备状态（1-未激活，2-禁用，3-在线，4-离线）
        device.setStatus(3l);
        device.setActiveTime(DateUtils.getNowDate());
        device.setCreateTime(DateUtils.getNowDate());

        IotProduct product = iotProductMapper.selectIotProductByProductId(productId);
        if (product == null) {
            log.error("自动添加设备时，根据产品ID查找不到对应产品");
            return 0;
        }
        int random = (int) (Math.random() * (90)) + 10;
        device.setDeviceName(product.getProductName() + random);
        device.setImgUrl(product.getImgUrl());
        device.setProductId(product.getProductId());
        device.setProductName(product.getProductName());
        iotDeviceMapper.insertIotDevice(device);

        // 缓存设备状态
        cacheDeviceStatus(device.getProductId(), device.getSerialNumber());

        // 添加设备用户
        SysUser sysUser = getLoginUser().getUser();
        IotDeviceUser deviceUser = new IotDeviceUser();
        deviceUser.setUserId(userId);
        deviceUser.setUserName(sysUser.getUserName());
        deviceUser.setPhonenumber(sysUser.getPhonenumber());
        deviceUser.setDeviceId(device.getDeviceId());
        deviceUser.setDeviceName(device.getDeviceName());
        deviceUser.setIsOwner(1);
        return iotDeviceUserMapper.insertDeviceUser(deviceUser);
    }

    /**
     * 根据设备编号查询设备认证信息
     */
    @Override
    public ProductAuthenticateModel selectProductAuthenticate(AuthenticateInputModel model) {
        ProductAuthenticateModel authenticateModel = iotDeviceMapper.selectProductAuthenticate(model);
        DeviceMqttConnectVO mqttConnectVO = this.getMqttConnectData(authenticateModel.getDeviceId());
        authenticateModel.setMqttAccount(mqttConnectVO.getUsername());
        authenticateModel.setMqttPassword(mqttConnectVO.getPasswd());
        authenticateModel.setMqttSecret(mqttConnectVO.getMqttsecret());
        return authenticateModel;
    }

    /**
     * 批量更新设备状态
     *
     * @param serialNumbers 设备ids
     * @param status        状态
     */
    @Override
    public void batchChangeStatus(List<String> serialNumbers, DeviceStatus status) {
        if (CollectionUtils.isEmpty(serialNumbers)) {
            return;
        }
        //设备离线
        if (DeviceStatus.OFFLINE.equals(status)) {
            iotDeviceMapper.batchChangeOffline(serialNumbers);
        } else if (DeviceStatus.ONLINE.equals(status)) {
            iotDeviceMapper.batchChangeOnline(serialNumbers);
        }
        deviceCache.updateBatchDeviceStatusCache(serialNumbers, status);
    }

    /**
     * 更新关联子设备
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDevices(IotDevicesInput iotDevice)
    {
        String identifier = iotDevice.getIdentifier();
        //添加设备关系
        IotThingsModelValue deviceValue  = new IotThingsModelValue();
        deviceValue.setDeviceId(iotDevice.getDeviceId());
        deviceValue.setIdentifier(identifier);
        deviceValue.setModelId(iotDevice.getModelId());
        deviceValue.setDeviceIds(iotDevice.getDeviceIds());
        // 根据设备标识处理不同类型的设备
        if(identifier.contains("_")){
            String id = identifier.split("_")[1] + "_" + identifier.split("_")[0];
            IotThingsModel relatedDevice = getDeviceByIdentifier(id);

            IotThingsModelValue relatedDeviceValue = new IotThingsModelValue();
            relatedDeviceValue.setIdentifier(relatedDevice.getIdentifier());
            relatedDeviceValue.setModelId(relatedDevice.getModelId());
            relatedDeviceValue.setValue(deviceValue.getDeviceId().toString());

            Long[] deviceIds = deviceValue.getDeviceIds();
            processDeviceRelation(deviceValue, relatedDeviceValue, deviceIds);
        }

        return 1;
    }

    public Long[] convertToLongArray(String value)  throws ServiceException {
        if (value == null || value.isEmpty()) {
            return new Long[0]; // 返回空数组
        }
        String[] stringValues = value.split(",");

        // 创建一个 HashSet 用来检查重复值
        Set<Long> encounteredValues = new HashSet<>();
        List<Long> longList = new ArrayList<>();

        for (String str : stringValues) {
            if (!str.isEmpty()) {
                try {
                    Long valueLong = Long.parseLong(str.trim());
                    if (encounteredValues.contains(valueLong)) {
                        throw new ServiceException("不能添加相同的设备ID");
                    } else {
                        encounteredValues.add(valueLong);
                        longList.add(valueLong);
                    }
                } catch (NumberFormatException e) {
                    //非long类型处理
                }
            }
        }

        // 将列表转换为 long[] 数组
        Long[] result = new Long[longList.size()];
        for (int i = 0; i < longList.size(); i++) {
            result[i] = longList.get(i);
        }

        return result;
    }

    // 根据设备标识获取设备信息
    private IotThingsModel getDeviceByIdentifier(String identifier)  throws ServiceException {
        IotThingsModel thingsModel = new IotThingsModel();
        thingsModel.setIdentifier(identifier);
        List<IotThingsModel> deviceList = iotThingsModelService.selectThingsModelList(thingsModel);
        if (deviceList == null || deviceList.isEmpty()) {
            throw new ServiceException("未查到标识符为" + identifier + "的物模型");
        }
        return deviceList.get(0);
    }

    // 处理设备关系（包括子设备和父设备）
    private void processDeviceRelation(IotThingsModelValue deviceValue, IotThingsModelValue relatedDeviceValue, Long[] deviceIds) {
        if (deviceIds != null && deviceIds.length != 0) {
            String deviceIdsString = Arrays.toString(deviceIds);
            deviceIdsString = deviceIdsString.substring(1, deviceIdsString.length() - 1);
            deviceValue.setValue(deviceIdsString);
            int count = iotThingsModelValueMapper.selectSubDevicesCount(deviceValue);

            if (count == 0) {
                // 如果没有记录，则插入新记录
                iotThingsModelValueMapper.insertMqttValue(deviceValue);
                processRelatedDevices(deviceIds, relatedDeviceValue);
            } else {
                // 如果记录已存在，更新并删除旧的父设备记录，再重新插入
                iotThingsModelValueMapper.updateMqttValue(deviceValue);
                Map<String, Object> params = new HashMap<>();
                params.put("value", relatedDeviceValue.getValue());
                params.put("identifier", relatedDeviceValue.getIdentifier());
                iotThingsModelValueMapper.deleteParentDeviceValue(params);
                processRelatedDevices(deviceIds, relatedDeviceValue);
            }
        } else {
            // 处理没有子设备的情况
            Map<String, Object> paramsS = new HashMap<>();
            paramsS.put("deviceId", deviceValue.getDeviceId());
            paramsS.put("identifier", deviceValue.getIdentifier());
            iotThingsModelValueMapper.deleteSubDevicesValue(paramsS);
            Map<String, Object> paramsP = new HashMap<>();
            paramsP.put("value", relatedDeviceValue.getValue());
            paramsP.put("identifier", relatedDeviceValue.getIdentifier());
            iotThingsModelValueMapper.deleteParentDeviceValue(paramsP);
        }
    }

    // 处理父设备或子设备的插入操作
    private void processRelatedDevices(Long[] deviceIds, IotThingsModelValue relatedDeviceValue) {
        for (Long deviceId : deviceIds) {
            relatedDeviceValue.setDeviceId(deviceId);
            iotThingsModelValueMapper.insertMqttValue(relatedDeviceValue);
        }
    }

    /**
     * 获取关联子设备ID数组
     */
    @Override
    public Long[] getDeviceIds(Long deviceId)
    {
        IotThingsModelValue list=iotThingsModelValueMapper.selectSubDevices(deviceId);
        Long[] deviceIds = new Long[0];
        // 检查 list 或 list.getValue() 是否为 null
        if (list != null && list.getValue() != null && !list.getValue().isEmpty()) {
            // 去掉方括号，并按逗号分割
//            String valueString = list.getValue().replaceAll("[\\[\\]]", "");
            String[] values = list.getValue().split(",");

            if (values.length > 0) {
                deviceIds = new Long[values.length];
                for (int i = 0; i < values.length; i++) {
                    try {
                        deviceIds[i] = Long.parseLong(values[i].trim());
                    } catch (NumberFormatException e) {
                        // 如果某个值无法转换为 Long，记录日志或采取其他措施
                        System.err.println("Invalid value: " + values[i]);
                        deviceIds[i] = null;  // 或者采取其他措施
                    }
                }
            }
        }
        return deviceIds;
    }

    //数组去掉id
    private String deleteIotValue(String iotValue, String deviceValue) {
        // 1. 将iotValue按逗号分割成数组
        String[] values = iotValue.split(",");
        // 2. 创建StringBuilder用于构建新的iotValue
        StringBuilder updatedIotValue = new StringBuilder();
        // 3. 遍历values数组，删除与deviceValue相同的元素
        boolean removedDeviceValue = false;
        for (int i = 0; i < values.length; i++) {
            if (values[i].equals(deviceValue)) {
                removedDeviceValue = true;  // 标记已移除
                continue;  // 跳过该项
            }
            // 如果不是删除项，将其加入updatedIotValue
            if (updatedIotValue.length() > 0) {
                updatedIotValue.append(",");
            }
            updatedIotValue.append(values[i]);
        }
        // 4. 处理逗号的问题，补充到原始的逗号数量
        // 计算原始iotValue的逗号数量
        int originalCommaCount = iotValue.length() - iotValue.replace(",", "").length();
        // 计算更新后的iotValue中的逗号数量
        int updatedCommaCount = updatedIotValue.length() - updatedIotValue.toString().replace(",", "").length();
        // 如果原始逗号数量大于更新后逗号数量，则补充多余的逗号
        int additionalCommas = originalCommaCount - updatedCommaCount;
        for (int i = 0; i < additionalCommas; i++) {
            updatedIotValue.append(",");
        }
        return updatedIotValue.toString();
    }
    private String insertIotValue(String iotValue, String deviceValue)  throws ServiceException {

        // 判断iotValue是否以逗号结尾
        if (!iotValue.endsWith(",")) {
            throw new ServiceException("添加超出数组范围");
        }

        StringBuilder updatedValue = new StringBuilder();
        boolean inserted = false;
        int len = iotValue.length();
        // 遍历iotValue
        for (int i = 0; i < len; i++) {
            char currentChar = iotValue.charAt(i);
            // 判断逗号前是否有值
            if (currentChar == ',' && (i == 0 || iotValue.charAt(i - 1) == ',')) {
                // 如果逗号前没有值，插入deviceValue在逗号前
                if (!inserted) {
                    updatedValue.append(deviceValue);
                    inserted = true;
                }
            }
            // 按正常顺序追加字符
            updatedValue.append(currentChar);
            // 如果遇到结尾的逗号，插入deviceValue在逗号后
            if (currentChar == ',' && i == len - 1) {
                if (!inserted) {
                    updatedValue.append(deviceValue);
                    inserted = true;
                }
            }
        }
        // 如果没有插入deviceValue，则默认添加到末尾
        if (!inserted) {
            updatedValue.append(deviceValue);
        }
        return updatedValue.toString();

    }

    private  String swapParts(String input) {
        if (input.contains("_")) {
            String[] parts = input.split("_");
            return parts[1] + "_" + parts[0];
        }
        return input;
    }

    /**
     * 根据产品ID获取产品下所有编号
     * @param productId
     * @return
     */
    public String[] getDeviceNumsByProductId(Long productId){
        return iotDeviceMapper.getDeviceNumsByProductId(productId);
    }

    /**
     * 获取所有已经激活并不是禁用的设备
     * @return
     */
    @Override
    public List<DeviceStatusVO> selectDeviceActive(){
        return iotDeviceMapper.selectDeviceActive();
    }

    /**
     * 获取设备物模型遥测属性值
     */
    @Override
    public IotDeviceShortOutput selectThingsModelList(Long deviceId)
    {
        // 查询设备信息
        IotDeviceShortOutput device = iotDeviceMapper.selectDeviceRunningStatusByDeviceId(deviceId);
        if(device==null){
            throw new ServiceException("未查询到该设备");
        }
        //根据产品ID更新JSON物模型
        List<IotThingsModel> iotThingsModels =iotThingsModelMapper.selectModelListByProductId(device.getProductId());
        List<ThingsModelJson> thingsModels = processThingsModels(iotThingsModels, device);
        device.setThingsModels(thingsModels);

        //获取关系设备遥测属性物模值
        List<IotThingsModelValue> relatedThingsModelValues = iotThingsModelValueMapper.selectGXThingsModelValue(deviceId);
        processRelatedThingsModels(relatedThingsModelValues, device);

        return device;
    }

    /**
     * 处理物模型列表并生成 ThingsModelJson 对象
     */
    private List<ThingsModelJson> processThingsModels(List<IotThingsModel> iotThingsModels, IotDeviceShortOutput device) {
        List<ThingsModelJson> thingsModels = new ArrayList<>();
        for (IotThingsModel iotThingsModel : iotThingsModels) {
            if (isTelemetryProperty(iotThingsModel)) {
                ThingsModelJson thingsModel = createThingsModelJson(iotThingsModel, device);
                thingsModels.add(thingsModel);
            }
        }
        return thingsModels;
    }

    /**
     * 判断是否为遥测属性物模型
     */
    private boolean isTelemetryProperty(IotThingsModel iotThingsModel) {
        return iotThingsModel.getType() == 1 && iotThingsModel.getSubType() == 3;
    }

    /**
     * 创建 ThingsModelJson 对象
     */
    private ThingsModelJson createThingsModelJson(IotThingsModel iotThingsModel, IotDeviceShortOutput device) {
        // 设置基础属性
        ThingsModelJson thingsModel = new ThingsModelJson();
        iotThingsModelServiceImpl.getAttribute(iotThingsModel);
        thingsModel.setId(iotThingsModel.getIdentifier());
        thingsModel.setName(iotThingsModel.getModelName());
        thingsModel.setIsChart(iotThingsModel.getIsChart());
        thingsModel.setIsHistory(iotThingsModel.getIsHistory());
        thingsModel.setIsMonitor(iotThingsModel.getIsMonitor());
        thingsModel.setIsRequired(iotThingsModel.getIsRequired());
        thingsModel.setIsReadonly(iotThingsModel.getIsReadonly());
        thingsModel.setIsUnique(iotThingsModel.getIsUnique());
        thingsModel.setType(iotThingsModel.getType().intValue());
        thingsModel.setSubType(iotThingsModel.getSubType().intValue());
        thingsModel.setOrder(iotThingsModel.getModelOrder().intValue());
        thingsModel.setModelId(iotThingsModel.getModelId());

        Datatypes datatype = new Datatypes();
        // 解析 JSON 字符串
        JSONObject jsonObject = JSONObject.parseObject(iotThingsModel.getSpecs());

        // 提取 unit 的值，并进行空值检查
        String unitValue = jsonObject.containsKey("unit") ? jsonObject.getString("unit") : null;

        if (unitValue != null && !unitValue.isEmpty()) {
            datatype.setUnit(unitValue);
        }
        thingsModel.setDatatype(datatype);
        // 获取遥测属性物模值
        DeviceLog deviceLog = getDeviceLog(device, iotThingsModel.getIdentifier());
        if(deviceLog!=null){
            thingsModel.setValue(deviceLog.getLogValue());
            thingsModel.setTs(deviceLog.getCreateTime().toString());
        }
        return thingsModel;
    }

    /**
     * 获取遥测属性物模值
     */
    private DeviceLog getDeviceLog(IotDeviceShortOutput device, String identifier) {
        DeviceLog deviceLog = new DeviceLog();
        deviceLog.setIdentity(identifier);
        deviceLog.setSerialNumber(device.getSerialNumber());
        return logService.selectThingsModelValue(deviceLog);
    }

    /**
     * 处理关系设备的遥测属性物模值
     */
    private void processRelatedThingsModels(List<IotThingsModelValue> relatedThingsModelValues, IotDeviceShortOutput device) {
        List<ThingsModelJson> thingsModels = new ArrayList<>();
        for (IotThingsModelValue relatedModel : relatedThingsModelValues) {
            ThingsModelJson thingsModel = new ThingsModelJson();
            thingsModel.setId(relatedModel.getIdentifier());
            thingsModel.setName(relatedModel.getModelName());
            thingsModel.setType(relatedModel.getType().intValue());

            String value = relatedModel.getValue();
            thingsModel.setValue(value);

            if (!value.isEmpty()) {
                Long[] deviceIds = convertToLongArray(value);
                List<Datatypes> datatypes = new ArrayList<>();
                for (Long deviceId : deviceIds) {
                    IotDeviceShortOutput relatedDevice = iotDeviceMapper.selectDeviceRunningStatusByDeviceId(deviceId);
                    if (relatedDevice != null) {
                        List<IotThingsModel> relatedThingsModels = iotThingsModelMapper.selectModelListByProductId(relatedDevice.getProductId());
                        List<ThingsModelJson> params = processThingsModels(relatedThingsModels, relatedDevice);
                        if(params.size() > 0) {
                            Datatypes datatype = new Datatypes();
                            datatype.setDeviceId(relatedDevice.getDeviceId());
                            datatype.setDeviceName(relatedDevice.getDeviceName());
                            datatype.setSerialNumber(relatedDevice.getSerialNumber());
                            datatype.setParams(params);
                            datatypes.add(datatype);
                        }
                    }
                }
                thingsModel.setDatatypes(datatypes);
            }
            thingsModels.add(thingsModel);
        }
        device.getThingsModels().addAll(thingsModels);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importDevice(List<IotDeviceImportVO> deviceImportVOList, Long productId) {
        LoginUser loginUser = getLoginUser();
        IotProduct product = iotProductMapper.selectIotProductByProductId(productId);
        if (null == product) {
            return "导入失败，产品信息为空";
        }
        List<String> serialNumberList = deviceImportVOList.stream().map(IotDeviceImportVO::getSerialNumber).collect(Collectors.toList());
        List<String> oldSerialNumberList = iotDeviceMapper.checkExistBySerialNumbers(serialNumberList);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(oldSerialNumberList)) {
            return "以下设备编号（" + JSON.toJSONString(oldSerialNumberList) + ")已存在，请修改后重试";
        }
        List<IotDevice> deviceList = new ArrayList<>();
        for (IotDeviceImportVO deviceImportVO : deviceImportVOList) {
            IotDevice device = new IotDevice();
            device.setDeviceName(deviceImportVO.getDeviceName());
            if (StringUtils.isEmpty(deviceImportVO.getSerialNumber())) {
                device.setSerialNumber(this.generationDeviceNum(1));
            } else {
                device.setSerialNumber(deviceImportVO.getSerialNumber());
            }
            device.setProductId(product.getProductId());
            device.setProductName(product.getProductName());
            device.setCreateBy(loginUser.getUserId().toString());
            device.setImgUrl(product.getImgUrl());
            deviceList.add(device);
        }
        // 批量插入设备
        int result = iotDeviceMapper.insertBatchDevice(deviceList);
        //新增设备分组
        // 获取所有插入成功的设备 ID
        List<Long> deviceIdList = deviceList.stream()
                .map(IotDevice::getDeviceId)
                .collect(Collectors.toList());

        // 新增：处理设备分组绑定
        for (int i = 0; i < deviceImportVOList.size(); i++) {
            IotDeviceImportVO vo = deviceImportVOList.get(i);
            String groupName = vo.getGroupName();

            if (StringUtils.isNotBlank(groupName)) {
                Long groupId = iotDeviceGroupMapper.selectGroupIdByGroupName(groupName);
                if (groupId != null && i < deviceIdList.size()) {
                    Long deviceId = deviceIdList.get(i);
                    // 构造参数并插入设备与分组关系
                    IotDeviceGroupInput input = new IotDeviceGroupInput();
                    input.setGroupId(groupId);
                    input.setDeviceIds(new Long[]{deviceId});
                    iotDeviceGroupMapper.insertDeviceGroups(input);
                }
            }
        }

        //redis缓存设备默认状态（物模型值）
        for(IotDevice iotDevice : deviceList){
            cacheDeviceStatus(productId, iotDevice.getSerialNumber());
        }

        return result > 0 ? "" : "导入失败";
    }

    /**
     * 查询产品下所有设备，返回设备编号
     *
     * @param productId 产品id
     * @return
     */
    @Override
    public List<IotDevice> selectDevicesByProductId(Long productId) {
        return iotDeviceMapper.selectDevicesByProductId(productId);
    }


//    public static void main(String[] args) {
//        try {
//            // 示例
//            String id = "parent_sub";
//            String result = swapParts(id);
//            System.out.println(result);  // 输出 "parent_sub"
//            // 测试 1
//            String iotValue1 = ",,,,";
//            String deviceValue1 = "87";
//            System.out.println("Updated iotValue: " + deleteIotValue(iotValue1, deviceValue1));  // 输出: 88,87
//
//            // 测试 2
//            String iotValue2 = "88,89,,";
//            String deviceValue2 = "87";
//            System.out.println("Updated iotValue: " + deleteIotValue(iotValue2, deviceValue2));  // 输出: 88,89,87,
//
//            // 测试 3
//            String iotValue4 = "88,89,,,,";
//            String deviceValue4 = "87";
//            System.out.println("Updated iotValue: " + deleteIotValue(iotValue4, deviceValue4));  // 抛出异常
//        } catch (Exception e) {
//            System.out.println(e.getMessage());  // 输出错误信息
//        }
//    }

}