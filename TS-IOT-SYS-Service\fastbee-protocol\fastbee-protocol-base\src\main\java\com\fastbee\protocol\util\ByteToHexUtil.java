package com.fastbee.protocol.util;

import org.apache.commons.lang3.ArrayUtils;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;

/**
 * Bytes和 Hex转换工具类
 */
public class ByteToHexUtil {

    /**
     * 单个Hex字符串转byte
     *
     * @param hexStr
     * @return
     */
    public static byte hexToByte(String hexStr) {
        return (byte) Integer.parseInt(hexStr, 16);
    }

    /**
     * hex字符串转为 byte数组
     *
     * @param inHex 转换的 hex字符串
     * @return 转换后的 byte数组
     */
    public static byte[] hexToByteArray(String inHex) {
        int hexlen = inHex.length();
        byte[] result;
        if (hexlen % 2 == 1) {
            // 奇数
            hexlen++;
            result = new byte[(hexlen / 2)];
            inHex = "0" + inHex;
        } else {
            // 偶数
            result = new byte[(hexlen / 2)];
        }
        int j = 0;
        for (int i = 0; i < hexlen; i += 2) {
            result[j] = hexToByte(inHex.substring(i, i + 2));
            j++;
        }
        return result;
    }

    /**
     * 单个字节转换十六进制
     *
     * @param b byte字节
     * @return 转换后的单个hex字符
     */
    public static String byteToHex(byte b) {
        String hex = Integer.toHexString(b & 0xFF);
        if (hex.length() < 2) {
            hex = "0" + hex;
        }
        return hex;
    }

    /**
     * byte数组转换炒年糕十六进制字符串
     *
     * @param bArray byte数组
     * @return hex字符串
     */
    public static String bytesToHexString(byte[] bArray) {
        StringBuffer sb = new StringBuffer(bArray.length);
        for (int i = 0; i < bArray.length; i++) {
            String hexStr = Integer.toHexString(0xFF & bArray[i]);
            if (hexStr.length() < 2)
                sb.append(0);
            sb.append(hexStr.toUpperCase());
        }
        return sb.toString();
    }





    /**
     * 将hex转为正负数 2个字节
     *
     * @param hexStr hex字符串
     * @return 结果
     */
    public static int parseHex2(String hexStr) {
        if (hexStr.length() != 4) {
            throw new NumberFormatException("Wrong length: " + hexStr.length() + ", must be 4.");
        }
        int ret = Integer.parseInt(hexStr, 16);
        ret = ((ret & 0x8000) > 0) ? (ret - 0x10000) : (ret);
        return  ret;
    }
    public static Integer cutMessageHexTo(byte[] source, int startIndex, int endIndex){
        byte[] subarray = ArrayUtils.subarray(source, startIndex, endIndex);
        String s = bytesToHexString(subarray);
        return Integer.parseInt(s,16);
    }



    public static void main(String[] args) {

        //String str = "000000000111";
        //byte[] result = hexToByteArray(str);
        //System.out.println("hex字符串转byte数组 ---" + Arrays.toString(result));
        //
        //System.out.println("hex字符串---"+bytesToHexString(result));
        //int parseInt = Integer.parseInt(str, 16);
        //System.out.println(parseInt);
        //
        //int va = -40;
        //byte[] bytes = IntegerToByteUtil.intToBytes2(va);
        //System.out.println("hex字符串转byte数组 ---" + Arrays.toString(bytes));
        //
        //int num = 1713;
        //String hexString = Integer.toHexString(num);
        //System.out.println(hexString);

        ScriptEngine js = new ScriptEngineManager().getEngineByName("JavaScript");
        String str = "8*%s";
        try {
            System.out.println(js.eval(str.replace("%s","0.42")));
        }catch (Exception e){

        }
    }

}
