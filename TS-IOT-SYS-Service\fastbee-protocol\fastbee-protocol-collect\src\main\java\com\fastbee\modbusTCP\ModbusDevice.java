package com.fastbee.modbusTCP;

import com.fastbee.common.core.protocol.Message;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * modbusTCP
 */
@NoArgsConstructor
@Data
public class ModbusDevice extends Message {

    /** 主键id */
    @ApiModelProperty("主键id")
    private Long id;

    /** 从机地址 */
    @ApiModelProperty("从机地址")
    private Integer slaveAddr;

    /** 从机ip地址 */
    @ApiModelProperty("从机ip")
    private String slaveIp;

    /** 从机名称 */
    @ApiModelProperty("从机名称")
    private String slaveName;

    /** 从机端口 */
    @ApiModelProperty("从机端口")
    private Integer slavePort;

    /** 寄存器起始地址(10进制) */
    @ApiModelProperty("寄存器起始地址(10进制)")
    private Long addrStart;

    /** 寄存器结束地址(10进制) */
    @ApiModelProperty("寄存器结束地址(10进制)")
    private Long addrEnd;

    /** 寄存器批量读取个数*/
    @ApiModelProperty("寄存器批量读取个数")
    private Integer packetLength;

    /** 轮询时间(默认5分钟) */
    @ApiModelProperty("轮询时间(默认5分钟)")
    private Long timer;

    @ApiModelProperty("解析类型")
    private String parseType;

    /**
     * 功能编码
     */
    @ApiModelProperty("功能编码")
    private Integer code;

    //modbus读取连接超时时间 ms 默认为10秒
    private Integer timeout;

    //连接超时时间 min 默认为5分钟
    private Integer timerout;

    public Integer getTimerout() {
        return timerout;
    }

    public void setTimerout(Integer timerout) {
        this.timerout = timerout;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer minutes) {
        this.timeout = minutes;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getSlaveAddr() {
        return slaveAddr;
    }

    public void setSlaveAddr(Integer slaveAddr) {
        this.slaveAddr = slaveAddr;
    }

    public String getSlaveIp() {
        return slaveIp;
    }

    public void setSlaveIp(String slaveIp) {
        this.slaveIp = slaveIp;
    }

    public String getSlaveName() {
        return slaveName;
    }

    public void setSlaveName(String slaveName) {
        this.slaveName = slaveName;
    }

    public Integer getSlavePort() {
        return slavePort;
    }

    public void setSlavePort(Integer slavePort) {
        this.slavePort = slavePort;
    }

    public Long getAddrStart() {
        return addrStart;
    }

    public void setAddrStart(Long addrStart) {
        this.addrStart = addrStart;
    }

    public Long getAddrEnd() {
        return addrEnd;
    }

    public void setAddrEnd(Long addrEnd) {
        this.addrEnd = addrEnd;
    }

    public Integer getPacketLength() {
        return packetLength;
    }

    public void setPacketLength(Integer packetLength) {
        this.packetLength = packetLength;
    }

    public Long getTimer() {
        return timer;
    }

    public void setTimer(Long timer) {
        this.timer = timer;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getParseType() {
        return parseType;
    }

    public void setParseType(String parseType) {
        this.parseType = parseType;
    }
}
