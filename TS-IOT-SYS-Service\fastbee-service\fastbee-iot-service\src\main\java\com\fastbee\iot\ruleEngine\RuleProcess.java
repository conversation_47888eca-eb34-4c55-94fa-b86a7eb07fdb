package com.fastbee.iot.ruleEngine;

import com.alibaba.fastjson2.JSON;
import com.fastbee.common.core.domain.entity.SysUser;
import com.fastbee.common.core.domain.model.LoginUser;
import com.fastbee.common.core.redis.RedisCache;
import com.fastbee.common.core.redis.RedisKeyBuilder;
import com.fastbee.common.exception.ServiceException;
import com.fastbee.device.domain.IotDevice;
import com.fastbee.device.service.IIotDeviceService;
import com.fastbee.iot.model.ProductCode;
import com.fastbee.iot.model.ScriptCondition;
import com.fastbee.iot.service.IScriptService;
import com.fastbee.framework.config.ProductMappingConfig;
import com.yomahub.liteflow.builder.el.LiteFlowChainELBuilder;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.fastbee.common.utils.SecurityUtils.getLoginUser;

/**
 * 执行规则引擎
 *
 * <AUTHOR>
 * @date 2024/2/3 16:07
 */
@Component
@Slf4j
public class RuleProcess {

    @Resource
    private FlowExecutor flowExecutor;
    @Resource
    private IScriptService scriptService;
    @Resource
    private RedisCache redisCache;
    @Autowired
    private IIotDeviceService deviceService;
    @Autowired
    private ProductMappingConfig productMappingConfig;

    /**
     * 规则引擎脚本处理
     *
     * @param topic
     * @param payload
     * @param event   1=设备上报 2=平台下发 3=设备上线 4=设备下线 （其他可以增加设备完成主题订阅之类）
     * @return
     */
    public List<MsgContext> processRuleScript(String serialNumber, int event, String topic, String payload) {
//        System.out.println("执行产品规则脚本...");
        ProductCode productCode = getDeviceDetail(serialNumber);
        if (Objects.isNull(productCode)){
            return Collections.emptyList();
        }

        //将产品分散
        Long productId = productCode.getProductId();
        List<Long> productIdsToProcess = new ArrayList<>();

        // 判断是否是特殊产品 ID（如 91），如果是，则映射到多个产品 ID 处理
        addProductIdsToProcess(productIdsToProcess, productId);

        // 使用原始上下文作为模板
        List<MsgContext> allContexts = new ArrayList<>();
        MsgContext baseContext = new MsgContext(topic, payload, serialNumber, productId, productCode.getProtocolCode());

        for (Long currentProductId : productIdsToProcess) {
            try {
                // 查询数据流脚本组件
                ScriptCondition scriptCondition = new ScriptCondition();
                scriptCondition.setProductId(currentProductId);
                scriptCondition.setScriptEvent(event);    // 事件 1=设备上报 2=平台下发 3=设备上线 4=设备下线
                scriptCondition.setScriptPurpose(1);  // 脚本用途：数据流=1
                scriptCondition.setEnable(1);
                String[] scriptIds = scriptService.selectRuleScriptIdArray(scriptCondition);

                //如果查询不到脚本，则认为是不用处理
                if (scriptIds == null || scriptIds.length == 0) {
                    continue;
                }

                // 构建新的上下文，复制原始内容并修改 productId
                MsgContext context = new MsgContext(
                        baseContext.getTopic(),
                        baseContext.getPayload(),
                        baseContext.getSerialNumber(),
                        currentProductId,
                        baseContext.getProtocolCode()
                );

                // 动态构造Chain和EL表达式
                String el = String.join(",", scriptIds); // THEN（a,b,c,d）
                LiteFlowChainELBuilder.createChain().setChainName("dataChain").setEL("THEN(" + el + ")").build();

                // 执行规则脚本
                LiteflowResponse response = flowExecutor.execute2Resp("dataChain", null, context);
                if (!response.isSuccess()) {
                    log.error("产品 {} 规则脚本执行发生错误：{}", currentProductId, response.getMessage());
                }
                // 添加到结果列表
                allContexts.add(context);

            } catch (Exception e) {
                log.error("处理产品 {} 的数据流时出错", currentProductId, e);
            }
        }

        // 返回原始上下文即可（用于后续可能的默认处理）
        return allContexts;
    }


    // 判断是否是特殊产品 ID（如 91），如果是，则映射到多个产品 ID 处理
    public void addProductIdsToProcess(List<Long> targetList, Long productId) {
        if (productId == null) {
            return;
        }
        List<Long> mappedList = productMappingConfig.getMapping().get(productId);
        if (mappedList != null && !mappedList.isEmpty()) {
            targetList.addAll(mappedList);
        } else {
            targetList.add(productId);
        }
    }


    /**
     * 查询产品id,协议编号，缓存到redis,后续查询协议的地方替换数据库查询
     *
     * @param serialNumber
     */
    public ProductCode getDeviceDetail(String serialNumber) {
        ProductCode productCode = new ProductCode();
        String cacheKey = RedisKeyBuilder.buildDeviceMsgCacheKey(serialNumber);
        if (redisCache.containsKey(cacheKey)) {
            Object cacheObject = redisCache.getCacheObject(cacheKey);
            return JSON.parseObject(cacheObject.toString(), ProductCode.class);
        }
        IotDevice device = deviceService.selectDeviceNoModel(serialNumber);
        if (device == null) {
            // 设备不存在，不继续处理
            return null;
        }
        productCode.setProtocolCode("JSON");
        productCode.setSerialNumber(serialNumber);
        productCode.setProductId(device.getProductId());
        try {
            LoginUser loginUser = getLoginUser();
            SysUser sysUser = loginUser.getUser();
            productCode.setUserId(sysUser.getUserId());
        } catch (ServiceException e) {

        }
        String jsonString = JSON.toJSONString(productCode);
        redisCache.setCacheObject(cacheKey, jsonString);
        return productCode;
    }
}
