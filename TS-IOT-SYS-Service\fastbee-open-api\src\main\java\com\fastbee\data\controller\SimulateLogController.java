package com.fastbee.data.controller;

import com.fastbee.common.annotation.Log;
import com.fastbee.common.core.controller.BaseController;
import com.fastbee.common.core.domain.AjaxResult;
import com.fastbee.common.core.mq.message.MqttBo;
import com.fastbee.common.core.page.TableDataInfo;
import com.fastbee.common.enums.BusinessType;
import com.fastbee.iot.domain.SimulateLog;
import com.fastbee.iot.service.ISimulateLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 模拟设备日志Controller
 * <AUTHOR>
 * @date 2023/4/6 17:06
 */
@Api(tags = "模拟设备日志")
@RestController
@RequestMapping("/iot/simulate")
public class SimulateLogController extends BaseController {

    @Resource
    private ISimulateLogService simulateLogService;

    /**
     * 查询模拟设备日志列表
     */
    @ApiOperation("查询模拟设备日志列表")
    @PreAuthorize("@ss.hasPermi('iot:simulate:list')")
    @GetMapping("/list")
    public TableDataInfo list(SimulateLog simulateLog) {
        startPage();
        List<MqttBo> list = simulateLogService.selectSimulateLogList(simulateLog);
        return getDataTable(list);
    }

    /**
     * 获取模拟设备日志详细信息
     */
    @ApiOperation("获取模拟设备日志详细信息")
    @PreAuthorize("@ss.hasPermi('iot:simulate:query')")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") Long logId) {
        return success(simulateLogService.selectSimulateLogByLogId(logId));
    }

    /**
     * 新增模拟设备日志
     */
    @ApiOperation("新增模拟设备日志")
    @PreAuthorize("@ss.hasPermi('iot:simulate:add')")
    @Log(title = "模拟设备日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SimulateLog simulateLog) {
        return toAjax(simulateLogService.insertSimulateLog(simulateLog));
    }

    /**
     * 修改模拟设备日志
     */
    @ApiOperation("修改模拟设备日志")
    @PreAuthorize("@ss.hasPermi('iot:simulate:edit')")
    @Log(title = "模拟设备日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SimulateLog simulateLog) {
        return toAjax(simulateLogService.updateSimulateLog(simulateLog));
    }

    /**
     * 删除模拟设备日志
     */
    @ApiOperation("删除模拟设备日志")
    @PreAuthorize("@ss.hasPermi('iot:simulate:remove')")
    @Log(title = "模拟设备日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds) {
        return toAjax(simulateLogService.deleteSimulateLogByLogIds(logIds));
    }
}