package com.fastbee.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fastbee.common.core.domain.entity.SysRoleIndex;

import java.util.List;

/**
 * 色首页配置
 */
public interface SysRoleIndexMapper extends BaseMapper<SysRoleIndex> {
    /**
     * 查询角色首页配置列表
     */
    List<SysRoleIndex> selectRoleIndexList(SysRoleIndex sysRoleIndex);

    /**
     * 根据角色列表查询首页配置
     */
    List<SysRoleIndex> selectRoleIndexByRoles(SysRoleIndex sysRoleIndex);

    /**
     * 根据ID查询角色首页配置
     */
    SysRoleIndex selectRoleIndexById(String id);

    /**
     * 新增角色首页配置
     */
    int insertRoleIndex(SysRoleIndex sysRoleIndex);

    /**
     * 修改角色首页配置
     */
    int updateRoleIndex(SysRoleIndex sysRoleIndex);

    /**
     * 删除角色首页配置
     */
    int deleteRoleIndexById(String id);

    /**
     * 批量删除角色首页配置
     */
    int deleteRoleIndexByIds(String[] ids);
}
