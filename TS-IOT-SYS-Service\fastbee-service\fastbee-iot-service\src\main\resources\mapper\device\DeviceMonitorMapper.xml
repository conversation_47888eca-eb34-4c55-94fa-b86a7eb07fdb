<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastbee.device.mapper.DeviceMonitorMapper">

    <resultMap type="com.fastbee.device.domain.DeviceMonitor.ProductDeviceCount" id="ProductDeviceCountResult">
        <result property="productId"    column="product_id"    />
        <result property="productName"    column="product_name"    />
        <result property="deviceCount"    column="deviceCount"    />
    </resultMap>

    <resultMap type="com.fastbee.device.domain.DeviceMonitor.DeviceStatusCount" id="DeviceStatusCountResult">
        <result property="totalCount"    column="totalCount"    />
        <result property="installedCount"    column="installedCount"    />
        <result property="removedCount"    column="removedCount"    />
        <result property="recycledCount"    column="recycledCount"    />
        <result property="scrappedCount"    column="scrappedCount"    />
    </resultMap>

    <resultMap type="com.fastbee.device.domain.IotProduct" id="IotProductResult">
        <result property="productId"    column="product_id"    />
        <result property="productName"    column="product_name"    />
        <result property="categoryId"    column="category_id"    />
        <result property="status"    column="status"    />
        <result property="deviceType"    column="device_type"    />
        <result property="imgUrl"    column="img_url"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark"    column="remark"    />
        <result property="modelId"    column="model_id"    />
    </resultMap>

    <select id="selectDeviceCountByProductId"  resultMap="ProductDeviceCountResult" >
        SELECT
            p.product_id,
            p.product_name,
            COUNT(d.device_id) AS deviceCount
        FROM iot_product p
        LEFT JOIN iot_device d
        ON p.product_id = d.product_id AND d.del_flag = '0'
        where p.status =2
        GROUP BY p.product_id, p.product_name
    </select>

    <select id="selectDeviceCounts" parameterType="String" resultMap="ProductDeviceCountResult" >
        SELECT
        ctv.category_id as product_id,
        ctv.category_name as product_name,
        COUNT(d2.device_id) AS deviceCount
        FROM category_tree_view ctv
        INNER JOIN iot_product p ON p.category_id = ctv.category_id AND p.status = 2
        LEFT JOIN iot_device d2 ON d2.product_id = p.product_id AND d2.del_flag = '0'
        LEFT JOIN iot_things_model_value dtmv ON dtmv.device_id = d2.device_id
        AND dtmv.identifier = 'device_status' AND dtmv.value = '0'
        <if test="groupIds != null and groupIds.length > 0">
            LEFT JOIN iot_device_group dg
            ON d2.device_id = dg.device_id
            AND dg.group_id IN
            <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        GROUP BY ctv.category_id, ctv.category_name
    </select>

    <select id="selectProductId" resultType="java.lang.Long">
        SELECT product_id
        FROM iot_product
        WHERE status = 2
          AND category_id = #{categoryId}
    </select>

    <select id="selectDeviceCountByGroupId" parameterType="Long" resultType="int">
        SELECT COUNT(dtmv.device_id) AS deviceCount
        FROM iot_things_model_value dtmv
            INNER JOIN  iot_device d2 ON dtmv.device_id = d2.device_id
            INNER JOIN  iot_device_group dg ON d2.device_id = dg.device_id
            INNER JOIN  iot_product p ON d2.product_id = p.product_id AND p.status = 2
        WHERE dtmv.identifier = 'device_status'
          AND dtmv.value = '0' AND dg.group_id = #{groupId}
          AND p.category_id IN (
            SELECT category_id FROM category_tree_view
        ) and p.status =2
    </select>

    <resultMap type="com.fastbee.device.domain.DeviceMonitor.IotDeviceInfo" id="IotDeviceInfoResult">
        <result property="serialNumber"    column="serial_number"    />
        <result property="deviceName"    column="device_name"    />
        <result property="status"    column="status"    />
    </resultMap>

    <select id="selectDeviceInfoByGroupId" resultMap="IotDeviceInfoResult">
        SELECT DISTINCT d2.serial_number, d2.device_name, d2.status
        FROM iot_things_model_value dtmv
            INNER JOIN iot_device d2 ON dtmv.device_id = d2.device_id
        <if test="groupIds != null and groupIds.length > 0">
            INNER JOIN iot_device_group dg
            ON d2.device_id = dg.device_id
            AND dg.group_id IN
            <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
            INNER JOIN iot_product p ON d2.product_id = p.product_id
        WHERE dtmv.identifier = 'device_status'
          AND dtmv.value = '0'
          AND p.category_id IN (
            SELECT category_id FROM category_tree_view
        ) and p.status =2
    </select>

    <select id="selectCountByProductId" resultType="int">
         SELECT COUNT(d2.serial_number) as count
         from iot_device d2
         INNER JOIN iot_product p ON d2.product_id = p.product_id AND p.status = 2
        <if test="groupIds != null and groupIds.length > 0">
            INNER JOIN iot_device_group dg
            ON d2.device_id = dg.device_id
            AND dg.group_id IN
            <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        AND p.category_id IN (
        SELECT category_id FROM category_tree_view
        )
        <where>
            <if test="productId != null and productId != ''">
                AND p.product_id = #{productId}
            </if>
        </where>
    </select>

    <select id="selectDeviceInfoByProductId" resultType="String">
        SELECT DISTINCT d2.serial_number
        FROM iot_things_model_value dtmv
        INNER JOIN iot_device d2 ON dtmv.device_id = d2.device_id
        <if test="groupIds != null and groupIds.length > 0">
            INNER JOIN iot_device_group dg
            ON d2.device_id = dg.device_id
            AND dg.group_id IN
            <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        INNER JOIN iot_product p ON d2.product_id = p.product_id
        WHERE dtmv.identifier = 'device_status'
        AND dtmv.value = '0'
        AND d2.status != 3
        <if test="productId != null and productId != ''">
            AND p.product_id = #{productId}
        </if>
        AND p.category_id IN (
        SELECT category_id FROM category_tree_view
        )
    </select>

    <select id="selectDeviceStatusCount"  resultMap="DeviceStatusCountResult" >
        SELECT
            COUNT(DISTINCT d2.device_id) AS totalCount,
            SUM(CASE WHEN dtmv.value = '0' THEN 1 ELSE 0 END) AS installedCount,
            SUM(CASE WHEN dtmv.value = '1' THEN 1 ELSE 0 END) AS removedCount,
            SUM(CASE WHEN dtmv.value = '2' THEN 1 ELSE 0 END) AS recycledCount,
            SUM(CASE WHEN dtmv.value = '3' THEN 1 ELSE 0 END) AS scrappedCount
        FROM iot_things_model_value dtmv
            INNER JOIN iot_device d2 ON dtmv.device_id = d2.device_id
            INNER JOIN iot_product p ON d2.product_id = p.product_id
        <if test="groupIds != null and groupIds.length > 0">
            INNER JOIN iot_device_group dg
            ON d2.device_id = dg.device_id
            AND dg.group_id IN
            <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        WHERE dtmv.identifier = 'device_status'
          AND p.category_id IN (
            SELECT category_id FROM category_tree_view
        ) and p.status =2
    </select>

    <select id="selectDeviceFaultsCount" resultType="String" >
        SELECT DISTINCT ial.serial_number
        FROM iot_alert_log ial
        INNER JOIN iot_product p ON ial.product_id = p.product_id
        and p.category_id IN (
        SELECT category_id FROM category_tree_view
        ) and p.status =2
        <if test="groupIds != null and groupIds.length > 0">
            INNER JOIN iot_device d
            ON ial.serial_number = d.serial_number
            INNER JOIN iot_device_group dg
            ON d.device_id = dg.device_id
            AND dg.group_id IN
            <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        <where>
            ial.alert_level = 4
            <if test="productId != null and productId != ''">
                AND ial.product_id = #{productId}
            </if>
            <if test="beginTime != null and endTime != null">
                AND ial.create_time between #{beginTime} and #{endTime}
            </if>
        </where>
    </select>

    <select id="selectCountByGroupId" resultType="int" >
        SELECT COUNT(DISTINCT ial.serial_number) AS count
        FROM iot_alert_log ial
        JOIN iot_product p ON ial.product_id = p.product_id
        and p.category_id IN (
        SELECT category_id FROM category_tree_view
        ) and p.status =2
        <if test="groupId != null and groupId != ''">
            INNER JOIN iot_device d2
            ON ial.serial_number = d2.serial_number
            INNER JOIN iot_device_group dg
            ON d2.device_id = dg.device_id
            AND dg.group_id = #{groupId}
        </if>
        <where>
            ial.alert_level = 4
            <if test="beginTime != null and endTime != null">
                AND ial.create_time between #{beginTime} and #{endTime}
            </if>
        </where>
    </select>

    <select id="selectFaultsCountBySN" parameterType="String"  resultType="int">
        SELECT EXISTS (
            SELECT 1
            FROM iot_alert_log
            WHERE serial_number = #{serialNumber}
              AND alert_level = 4
              AND create_time BETWEEN NOW() - INTERVAL 1 HOUR AND NOW()
            LIMIT 1
        ) AS hasData
    </select>

    <select id="selectAlertLogListCount" parameterType="com.fastbee.iot.domain.AlertLog" resultType="Long">
        SELECT d.device_id, d.device_name, d.product_id, d.product_name, d.serial_number, l.alert_log_id,
               l.alert_name, l.alert_level, l.status,l.detail, l.serial_number, l.create_time, l.remark
        FROM iot_alert_log l
        LEFT JOIN iot_device d ON l.serial_number=d.serial_number

    </select>

    <select id="selectIotProductList" resultMap="IotProductResult">
        SELECT product_id, product_name, category_id, status, device_type, img_url, del_flag, create_time, update_time,remark
        FROM iot_product
        WHERE category_id IN (
            SELECT category_id FROM category_tree_view
        ) and status =2
    </select>

    <resultMap type="com.fastbee.device.domain.DeviceMonitor.IotGroup" id="IotGroupResult">
        <result property="groupId"    column="group_id"    />
        <result property="groupName"    column="group_name"    />
        <result property="groupOrder"    column="group_order"    />
        <result property="parentId"    column="parent_id"    />
        <result property="level"    column="level"    />
    </resultMap>

    <select id="findGroupTree" parameterType="String" resultMap="IotGroupResult">
        WITH RECURSIVE group_tree AS (
            SELECT group_id, group_name, parent_id, group_order,0 AS level
            FROM iot_group
            WHERE group_name = #{groupName}
            UNION ALL
            SELECT g.group_id, g.group_name, g.parent_id, g.group_order, gt.level + 1 AS level
            FROM iot_group g
            INNER JOIN group_tree gt
            ON g.parent_id = gt.group_id
        )
        SELECT group_id, group_name, parent_id, group_order, level
        FROM group_tree
        ORDER BY level, group_order
    </select>

    <select id="selectDeviceFaultsList" resultType="com.fastbee.device.domain.DeviceMonitor.DeviceFaultList">
        SELECT t.serial_number AS serialNumber,
               ANY_VALUE(t.alert_log_id) AS alertLogId,
               ANY_VALUE(t.script_id) AS scriptId,
               ANY_VALUE(t.scene_id) AS sceneId,
               ANY_VALUE(t.alert_name) AS alertName,
               ANY_VALUE(t.alert_level) AS alertLevel,
               ANY_VALUE(t.status) AS status,
               ANY_VALUE(t.product_id) AS productId,
               JSON_ARRAYAGG(t.detail) AS detail,
               MAX(t.create_time) AS createTime,
               ANY_VALUE(t.alert_time) AS alertTime,
               ANY_VALUE(t.remark) AS suggestion,
               ANY_VALUE(t.device_name) AS deviceName
        FROM (
                 SELECT l.*
                 FROM iot_alert_log l
                          INNER JOIN (
                     SELECT serial_number, MAX(create_time) AS latest_create_time
                     FROM iot_alert_log
                     WHERE alert_level = 4
                     GROUP BY serial_number
                 ) latest ON l.serial_number = latest.serial_number AND l.create_time = latest.latest_create_time
             ) AS t
                 INNER JOIN iot_product p ON t.product_id = p.product_id
        WHERE p.category_id IN (SELECT category_id FROM category_tree_view) and p.status =2
        GROUP BY t.serial_number
        ORDER BY alertTime DESC
            LIMIT 1000
    </select>

    <select id="selectGroupNameBySN" parameterType="String" resultType="com.fastbee.device.domain.DeviceMonitor.DeviceFaultList">
        SELECT d.device_name AS deviceName, d.serial_number AS serialNumber, g.group_name AS groupName
        FROM iot_device d
         LEFT JOIN iot_device_group dg ON dg.device_id = d.device_id
         LEFT JOIN iot_group g ON g.group_id = dg.group_id
        WHERE d.serial_number = #{serialNumber}
    </select>

    <select id="selectBelongsDeviceNameBySN" parameterType="String" resultType="String">
        WITH RECURSIVE device_path AS (
            SELECT tmv.value AS next_id
            FROM iot_things_model_value tmv
            WHERE tmv.device_id = (SELECT device_id FROM iot_device WHERE serial_number = #{serialNumber})
              AND tmv.identifier = 'to_belongs'
            UNION ALL
            SELECT tmv.value
            FROM iot_things_model_value tmv
            JOIN device_path dp ON tmv.device_id = dp.next_id
            WHERE tmv.identifier = 'to_belongs'
        )
        SELECT d.device_name AS belongsDeviceName
        FROM device_path dp
         JOIN iot_device d ON dp.next_id = d.device_id
         LEFT JOIN iot_things_model_value tmv ON dp.next_id = tmv.device_id AND tmv.identifier = 'to_belongs'
        WHERE tmv.id IS NULL
    </select>


</mapper>