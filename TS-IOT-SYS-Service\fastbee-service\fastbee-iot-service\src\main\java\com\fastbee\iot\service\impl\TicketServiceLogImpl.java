package com.fastbee.iot.service.impl;

import com.fastbee.common.core.domain.entity.SysUser;
import com.fastbee.common.utils.DateUtils;
import com.fastbee.iot.domain.TicketLog;
import com.fastbee.iot.mapper.TicketLogMapper;
import com.fastbee.iot.service.ITicketLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

import static com.fastbee.common.utils.SecurityUtils.getLoginUser;

/**
 * 设备告警Service业务层处理
 */
@Service
public class TicketServiceLogImpl implements ITicketLogService
{
    @Autowired
    private TicketLogMapper ticketLogMapper;

    /**
     * 查询工时
     * @param logId 工时主键
     * @return 工时
     */
    @Override
    public TicketLog selectTicketLogByLogId(Long logId)
    {
        return ticketLogMapper.selectTicketLogByLogId(logId);
    }

    /**
     * 查询工时列表
     * @param ticketLog 工时
     * @return 工单集合
     */
    @Override
    public List<TicketLog> selectTicketLogList(TicketLog ticketLog)
    {
        return ticketLogMapper.selectTicketLogList(ticketLog);
    }

    /**
     * 新增工时
     * @param ticketLog 工时
     * @return 结果
     */
    @Override
    public int insertTicketLog(TicketLog ticketLog)
    {
        SysUser user = getLoginUser().getUser();
        ticketLog.setUserId(user.getUserName());
        ticketLog.setCreateBy(user.getUserName());
        ticketLog.setCreateTime(DateUtils.getNowDate());
        return ticketLogMapper.insertTicketLog(ticketLog);
    }

    /**
     * 修改工时
     * @param ticketLog 工时
     * @return 结果
     */
    @Override
    public int updateTicketLog(TicketLog ticketLog)
    {
        return ticketLogMapper.updateTicketLog(ticketLog);
    }

    /**
     * 批量删除工时
     * @param logIds 需要删除的数据主键集合
     * @return 结果
     */
    @Override
    public int deleteTicketLogByLogIds(Long[] logIds)
    {
        return ticketLogMapper.deleteTicketLogByLogIds(logIds);
    }

    /**
     * 删除工时
     * @param logId 工时主键
     * @return 结果
     */
    @Override
    public int deleteTicketLogByLogId(Long logId)
    {
        return ticketLogMapper.deleteTicketLogByLogId(logId);
    }

}
