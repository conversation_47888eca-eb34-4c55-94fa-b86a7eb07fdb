package com.fastbee.modbusTCP;

import com.fastbee.common.enums.ModbusDataType;
import lombok.extern.slf4j.Slf4j;
import java.io.*;
import java.net.*;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.Arrays;

@Slf4j
public class ModbusTcpClient {

    // 通用的读取操作方法
    public Object performReadOperation(ModbusDevice device, byte functionCode, ModbusDataType dataType) throws IOException {
        //log.info("Trying to connect to device at {}:{}", device.getSlaveIp(), device.getSlavePort());
        try (Socket socket = new Socket(device.getSlaveIp(), device.getSlavePort())) {
            //设置modbus读取连接超时时间默认为10秒
            Integer timeout = (device.getTimeout() == null || device.getTimeout() <= 0) ? 10000 : device.getTimeout();
            socket.setSoTimeout(timeout);
            OutputStream outputStream = socket.getOutputStream();
            InputStream inputStream = socket.getInputStream();

            // 构建请求消息
            ByteBuffer requestBuffer = ByteBuffer.allocate(12);
            requestBuffer.order(ByteOrder.BIG_ENDIAN);
            requestBuffer.putShort((short) 0);
            requestBuffer.putShort((short) 0);
            requestBuffer.putShort((short) 6);
            requestBuffer.put(device.getSlaveAddr().byteValue());
            requestBuffer.put(functionCode);
            requestBuffer.putShort(device.getAddrStart().shortValue());
            requestBuffer.putShort(device.getPacketLength().shortValue());

            // 发送请求
            //log.info("Sending request to device...");
            outputStream.write(requestBuffer.array());

            // 接收响应
            byte[] responseBytes = new byte[1024];
            //log.info("Waiting for response from device...");
            int bytesRead = inputStream.read(responseBytes);
            //log.info("Received {} bytes from device", bytesRead);
            byte[] actualResponse = Arrays.copyOf(responseBytes, bytesRead);

            // 解析响应
            if (actualResponse[7] == functionCode && bytesRead >= 9) {
                int byteCount = actualResponse[8];
                ByteBuffer responseBuffer = ByteBuffer.wrap(actualResponse, 9, byteCount);
                responseBuffer.order(ByteOrder.BIG_ENDIAN);
                switch (dataType) {
                    case BIT:
                        boolean[] bitResult = new boolean[device.getPacketLength()];
                        for (int i = 0; i < device.getPacketLength(); i++) {
                            byte b = responseBuffer.get();
                            for (int j = 0; j < 8 && (i * 8 + j) < device.getPacketLength(); j++) {
                                bitResult[i * 8 + j] = ((b >> (7 - j)) & 1) == 1;
                            }
                        }
                        return bitResult;
                    case U_SHORT:
                        short[] ushortResult = new short[device.getPacketLength()];
                        for (int i = 0; i < device.getPacketLength(); i++) {
                            ushortResult[i] = responseBuffer.getShort();
                        }
                        return ushortResult;
                    case SHORT:
                        short[] shortResult = new short[device.getPacketLength()];
                        for (int i = 0; i < device.getPacketLength(); i++) {
                            shortResult[i] = responseBuffer.getShort();
                        }
                        return shortResult;
                    case LONG_ABCD:
                        long[] longABCDResult = new long[device.getPacketLength() / 2];
                        for (int i = 0; i < device.getPacketLength() / 2; i++) {
                            longABCDResult[i] = ((long) responseBuffer.getInt() & 0xFFFFFFFFL);
                        }
                        return longABCDResult;
                    case LONG_CDAB:
                        long[] longCDABResult = new long[device.getPacketLength() / 2];
                        for (int i = 0; i < device.getPacketLength() / 2; i++) {
                            longCDABResult[i] = ((long) ((responseBuffer.getShort() & 0xFFFF) << 16) | (responseBuffer.getShort() & 0xFFFF));
                        }
                        return longCDABResult;
                    case U_LONG_ABCD:
                        long[] ulongABCDResult = new long[device.getPacketLength() / 2];
                        for (int i = 0; i < device.getPacketLength() / 2; i++) {
                            ulongABCDResult[i] = ((long) responseBuffer.getInt() & 0xFFFFFFFFL);
                        }
                        return ulongABCDResult;
                    case U_LONG_CDAB:
                        long[] ulongCDABResult = new long[device.getPacketLength() / 2];
                        for (int i = 0; i < device.getPacketLength() / 2; i++) {
                            ulongCDABResult[i] = ((long) ((responseBuffer.getShort() & 0xFFFF) << 16) | (responseBuffer.getShort() & 0xFFFF));
                        }
                        return ulongCDABResult;
                    case FLOAT_ABCD:
                        float[] floatABCDResult = new float[device.getPacketLength() / 2];
                        for (int i = 0; i < device.getPacketLength() / 2; i++) {
                            floatABCDResult[i] = responseBuffer.getFloat();
                        }
                        return floatABCDResult;
                    case FLOAT_CDAB:
                        float[] floatCDABResult = new float[device.getPacketLength() / 2];
                        for (int i = 0; i < device.getPacketLength() / 2; i++) {
                            // 避免四舍五入，保持原始精度
                            floatCDABResult[i] = Float.intBitsToFloat((responseBuffer.getShort() & 0xFFFF) << 16 | (responseBuffer.getShort() & 0xFFFF));
                        }
                        return floatCDABResult;
                    default:
                        throw new IllegalArgumentException("不支持的数据类型:"+dataType);
                }
            } else {
                throw new IOException("无效的响应：预期功能码为:" + functionCode + "，且字节读取数应大于或等于 9，但实际得到的功能码为:" + actualResponse[7] + "，字节读取数为:" + bytesRead);
            }
        } catch (SocketTimeoutException e) {
            log.error("在等待从设备 {}:{} 响应时，读取操作超时", device.getSlaveIp(), device.getSlavePort(), e);
            throw e;
        } catch (ConnectException e) {
            log.error("连接到设备 {}:{} 失败", device.getSlaveIp(), device.getSlavePort(), e);
            throw e;
        }
    }

    // 通用的写入操作方法
    public  void performWriteOperation(ModbusDevice device, byte functionCode, Object value) throws IOException {
        try (Socket socket = new Socket(device.getSlaveIp(), device.getSlavePort())) {
            OutputStream outputStream = socket.getOutputStream();
            InputStream inputStream = socket.getInputStream();

            ByteBuffer requestBuffer = ByteBuffer.allocate(12);
            requestBuffer.order(ByteOrder.BIG_ENDIAN);
            requestBuffer.putShort((short) 0);
            requestBuffer.putShort((short) 0);
            requestBuffer.putShort((short) 6);
            requestBuffer.put(device.getSlaveAddr().byteValue());
            requestBuffer.put(functionCode);
            requestBuffer.putShort(device.getAddrStart().shortValue());

            if (value instanceof Short) {
                requestBuffer.putShort((short) 1);
                requestBuffer.putShort((short) value);
            } else if (value instanceof byte[]) {
                byte[] valueBytes = (byte[]) value;
                requestBuffer.putShort((short) ((short) valueBytes.length / 2));
                requestBuffer.put((byte) valueBytes.length);
                requestBuffer.put(valueBytes);
            }

            // 发送请求
            outputStream.write(requestBuffer.array());

            // 接收响应
            byte[] responseBytes = new byte[1024];
            int bytesRead = inputStream.read(responseBytes);
            byte[] actualResponse = Arrays.copyOf(responseBytes, bytesRead);

            // 验证响应
            if (actualResponse[7] == functionCode && bytesRead == 12) {
                // 响应正确
            } else {
                throw new IOException("Invalid response");
            }
        }
    }
}

