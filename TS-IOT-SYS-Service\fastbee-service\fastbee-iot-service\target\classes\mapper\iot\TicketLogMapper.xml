<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastbee.iot.mapper.TicketLogMapper">

    <resultMap type="com.fastbee.iot.domain.TicketLog" id="TicketLogResult">
        <result property="logId"    column="log_id"    />
        <result property="ticketId"    column="ticket_id"    />
        <result property="hours"    column="hours"    />
        <result property="remark"    column="remark"    />
        <result property="userId"    column="user_id"    />
        <result property="logTime"    column="log_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectTicketLogVo">
        select log_id, ticket_id, hours, remark, user_id, log_time, create_by, create_time from iot_ticket_time_log
    </sql>

    <select id="selectTicketLogByLogId" parameterType="Long" resultMap="TicketLogResult">
        <include refid="selectTicketLogVo"/>
        where log_id = #{logId}
    </select>

    <select id="selectTicketLogList" parameterType="com.fastbee.iot.domain.TicketLog" resultMap="TicketLogResult">
        <include refid="selectTicketLogVo"/>
        <where>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
            <if test="ticketId != null "> and ticket_id = #{ticketId}</if>
            <if test="userId != null and userId != ''"> and user_id = #{userId}</if>
            <if test="logTime != null "> and log_time = #{logTime}</if>

        </where>
        order by create_time desc
    </select>

    <insert id="insertTicketLog" parameterType="com.fastbee.iot.domain.TicketLog" useGeneratedKeys="true" keyProperty="logId">
        insert into iot_ticket_time_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ticketId != null">ticket_id,</if>
            <if test="hours != null">hours,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="logTime != null">log_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ticketId != null">#{ticketId},</if>
            <if test="hours != null">#{hours},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="logTime != null">#{logTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateTicketLog" parameterType="com.fastbee.iot.domain.TicketLog">
        update iot_ticket_time_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="ticketId != null">ticket_id = #{ticketId},</if>
            <if test="hours != null">hours = #{hours},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="logTime != null">log_time = #{logTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteTicketLogByLogId" parameterType="Long">
        delete from iot_ticket_time_log where log_id = #{logId}
    </delete>

    <delete id="deleteTicketLogByLogIds" parameterType="String">
        delete from iot_ticket_time_log where log_id in
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>

</mapper>
