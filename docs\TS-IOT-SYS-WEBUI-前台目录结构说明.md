# TS-IOT-SYS-WEBUI 前台目录结构及功能说明

## 项目概述

TS-IOT-SYS-WEBUI 是物联网信息管理平台的前端部分，采用现代化的前端技术栈构建，为用户提供直观、高效的操作界面。系统支持设备管理、数据监控、告警通知、可视化展示等多种功能，并适配手机、平板、PC等多种终端设备。

## 技术架构

* 基础框架：Vue 3.x + CompositionAPI setup 语法糖
* 开发语言：TypeScript
* 构建工具：Vite
* UI 框架：Element Plus
* 状态管理：Pinia
* 路由管理：Vue Router 4
* 国际化：Vue-i18n
* 数据可视化：ECharts
* 富文本编辑：Quill

## 系统架构图

```mermaid
graph TD
    classDef mainModule fill:#f9f,stroke:#333,stroke-width:2px
    classDef subModule fill:#bbf,stroke:#333,stroke-width:1px
    
    A[TS-IOT-SYS-WEBUI]
    
    A --> B[核心模块]
    A --> C[业务功能模块]
    A --> D[公共组件]
    
    B --> B1[路由管理<br>router]
    B --> B2[状态管理<br>stores]
    B --> B3[API接口<br>api]
    B --> B4[类型定义<br>types]
    B --> B5[工具函数<br>utils]
    B --> B6[国际化<br>i18n]
    
    C --> C1[首页<br>home]
    C --> C2[物联网管理<br>iot]
    C --> C3[系统管理<br>system]
    C --> C4[数据可视化<br>scada]
    C --> C5[监控中心<br>monitor]
    C --> C6[通知管理<br>notify]
    C --> C7[智能分析<br>ai]
    
    D --> D1[布局组件<br>layout]
    D --> D2[自定义组件<br>components]
    D --> D3[主题管理<br>theme]
    D --> D4[指令集<br>directive]
    D --> D5[资源文件<br>assets]
    
    class A mainModule
    class B,C,D mainModule
    class B1,B2,B3,B4,B5,B6,C1,C2,C3,C4,C5,C6,C7,D1,D2,D3,D4,D5 subModule
```

## 目录结构及功能

### 1. 核心模块

#### 1.1 src/router（路由管理）

负责应用的路由配置和管理，定义了各个页面的访问路径、组件加载关系和权限控制。

**主要功能**：
- 路由配置
- 路由守卫
- 动态路由加载
- 权限控制

#### 1.2 src/stores（状态管理）

使用 Pinia 实现全局状态管理，存储和管理应用的共享数据。

**主要功能**：
- 用户信息管理
- 应用配置管理
- 主题设置
- 多语言控制
- 菜单状态管理

#### 1.3 src/api（API接口）

封装与后端服务交互的接口，按功能模块划分不同的API文件。

**主要目录**：
- `iot/`：物联网相关接口
- `system/`：系统管理接口
- `login/`：登录认证接口
- `scada/`：组态监控接口
- `monitor/`：监控相关接口
- `notify/`：通知管理接口

#### 1.4 src/types（类型定义）

TypeScript类型定义文件，为应用提供类型安全保障。

**主要功能**：
- 接口类型定义
- 组件props类型
- 状态管理类型
- 全局类型声明

#### 1.5 src/utils（工具函数）

提供各种通用的工具函数和辅助方法。

**主要功能**：
- HTTP请求封装
- 日期处理
- 数据格式转换
- 加密解密
- 本地存储

#### 1.6 src/i18n（国际化）

实现应用的多语言支持功能。

**主要功能**：
- 语言包管理
- 语言切换
- 文本翻译

### 2. 业务功能模块

#### 2.1 src/views/home（首页）

系统首页，展示系统概览、重要数据和快捷入口。

**主要功能**：
- 系统数据概览
- 设备状态统计
- 告警信息展示
- 快捷功能入口

#### 2.2 src/views/iot（物联网管理）

物联网设备和资源管理模块，是系统的核心业务功能。

**主要子模块**：
- `device/`：设备管理
- `product/`：产品管理
- `category/`：分类管理
- `group/`：分组管理
- `platform/`：平台管理
- `protocol/`：协议管理
- `alert/`：告警管理
- `scene/`：场景联动
- `template/`：模板管理
- `sip/`：SIP设备管理

#### 2.3 src/views/system（系统管理）

系统基础设置和用户权限管理。

**主要功能**：
- 用户管理
- 角色权限
- 菜单管理
- 部门管理
- 岗位管理
- 字典管理
- 参数设置
- 日志管理

#### 2.4 src/views/scada（数据可视化）

实现设备数据的可视化展示和组态功能。

**主要子模块**：
- `topo/`：拓扑图编辑和展示
- `echart/`：图表可视化
- `center/`：监控中心

#### 2.5 src/views/monitor（监控中心）

系统和设备运行状态监控。

**主要功能**：
- 服务监控
- 缓存监控
- 设备状态监控
- 连接池监控

#### 2.6 src/views/notify（通知管理）

系统通知和消息管理。

**主要功能**：
- 公告管理
- 消息通知
- 站内信

#### 2.7 src/views/ai（智能分析）

AI智能分析功能模块。

**主要功能**：
- 数据分析
- 趋势预测
- 智能诊断

### 3. 公共组件

#### 3.1 src/layout（布局组件）

定义应用的整体布局结构。

**主要功能**：
- 页面框架
- 导航菜单
- 页头页脚
- 侧边栏

#### 3.2 src/components（自定义组件）

项目中的公共组件和业务组件。

**主要功能**：
- 表单组件
- 表格组件
- 弹窗组件
- 图表组件
- 上传组件

#### 3.3 src/theme（主题管理）

管理应用的主题样式和配置。

**主要功能**：
- 主题切换
- 样式定义
- 动态主题

#### 3.4 src/directive（指令集）

Vue自定义指令集合。

**主要功能**：
- 权限控制指令
- 防抖节流指令
- 复制粘贴指令
- 拖拽指令

#### 3.5 src/assets（资源文件）

存放项目的静态资源文件。

**主要内容**：
- 图片资源
- 图标资源
- 样式文件
- 字体文件

## 技术特点

1. **组件化开发**：采用Vue3组件化开发，提高代码复用性和可维护性。

2. **TypeScript支持**：全面使用TypeScript进行开发，提供类型安全保障。

3. **响应式设计**：适配PC、平板、手机等多种终端设备。

4. **状态管理**：使用Pinia进行状态管理，替代Vuex，提供更好的TypeScript支持和更简洁的API。

5. **动态路由**：基于用户权限动态加载路由，实现精细化的权限控制。

6. **国际化**：支持多语言切换，满足国际化需求。

7. **主题定制**：支持主题切换和自定义，满足不同场景的UI需求。

8. **数据可视化**：集成ECharts实现丰富的数据可视化效果。

## 开发环境要求

- Node.js >= 14.18+ / 16+
- 推荐使用cnpm或yarn作为包管理工具
- 支持的浏览器：Edge ≥ 88、Firefox ≥ 78、Chrome ≥ 87、Safari ≥ 13

## 开发部署流程

```bash
# 克隆项目
git clone [项目地址]

# 进入项目目录
cd TS-IOT-SYS-WEBUI

# 安装依赖
cnpm install

# 启动开发服务器
cnpm run dev

# 构建生产环境版本
cnpm run build
```

## 总结

TS-IOT-SYS-WEBUI前端系统采用现代化的前端技术栈，实现了高效、美观、响应式的用户界面。系统架构清晰，模块划分合理，既满足了复杂物联网应用的功能需求，又保证了良好的用户体验和开发效率。通过与后端服务的紧密配合，为用户提供了完整的物联网信息管理解决方案。 