{"groups": [{"name": "rocketmq.producer", "type": "com.fastbee.mq.rocketmq.consumer.ConsumerTopicConstant", "sourceType": "com.fastbee.mq.rocketmq.consumer.ConsumerTopicConstant"}], "properties": [{"name": "rocketmq.producer.device-reply-topic", "type": "java.lang.String", "sourceType": "com.fastbee.mq.rocketmq.consumer.ConsumerTopicConstant"}, {"name": "rocketmq.producer.device-status-topic", "type": "java.lang.String", "sourceType": "com.fastbee.mq.rocketmq.consumer.ConsumerTopicConstant"}, {"name": "rocketmq.producer.device-up-topic", "type": "java.lang.String", "sourceType": "com.fastbee.mq.rocketmq.consumer.ConsumerTopicConstant"}, {"name": "rocketmq.producer.fetch-prop-topic", "type": "java.lang.String", "sourceType": "com.fastbee.mq.rocketmq.consumer.ConsumerTopicConstant"}, {"name": "rocketmq.producer.function-invoke-topic", "type": "java.lang.String", "sourceType": "com.fastbee.mq.rocketmq.consumer.ConsumerTopicConstant"}, {"name": "rocketmq.producer.mq-topic", "type": "java.lang.String", "description": "网关默认主题", "sourceType": "com.fastbee.mq.rocketmq.consumer.ConsumerTopicConstant"}], "hints": []}