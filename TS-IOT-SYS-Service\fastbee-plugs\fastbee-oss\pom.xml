<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fastbee-plugs</artifactId>
        <groupId>com.fastbee</groupId>
        <version>3.8.5</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>fastbee-oss</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.fastbee</groupId>
            <artifactId>fastbee-common</artifactId>
        </dependency>

        <!-- AWS S3 -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.12.272</version>
        </dependency>

    </dependencies>

</project>
