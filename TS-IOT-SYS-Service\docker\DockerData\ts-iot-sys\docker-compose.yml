networks:
  network:
    ipam:
      driver: default
      config:
        - subnet: '*********/17'

services:
  mysql:
    image: mysql:8.0.39
    container_name: mysql4tld
    ports:
      - 5981:3306
    privileged: true
    restart: always
    networks:
      network:
        ipv4_address: **********
    volumes:
      - /opt/DockerData/ts-iot-sys/mysql/mysql:/var/lib/mysql

      - /opt/DockerData/ts-iot-sys/mysql/initdb:/docker-entrypoint-initdb.d
    environment:
      MYSQL_DATABASE: fastbee5
      MYSQL_ROOT_PASSWORD: 123456
    command:
      [
        'mysqld',
        '--character-set-server=utf8',
        '--collation-server=utf8_unicode_ci',
        '--default-time-zone=+8:00',
        '--lower-case-table-names=1'
      ]

  redis:
    image: redis:7.0.0
    container_name: redis4tld
    ports:
      - 5862:6379
    privileged: true
    restart: always
    networks:
      network:
        ipv4_address: **********
    volumes:
      - /opt/DockerData/ts-iot-sys/redis:/usr/local/etc/redis
      - /opt/DockerData/ts-iot-sys/redis/data:/data
    command: [ '-- requirepass tldiot', '-- appendonly yes' ]

  java:
    image: git.978543210.com/iot-rd/ts-iot-sys-service:v2.2.1
    container_name: java4tld
    ports:
      - 6106:8080
      - 5864:1883
      - 5865:8083
      - 5867:5061/udp
    privileged: true
    restart: always
    networks:
      network:
        ipv4_address: **********
    depends_on:
      - emqx
      - redis
      - mysql
      - tdengine
      - zlmedia
    volumes:
      - /opt/DockerData/ts-iot-sys/java/resources:/app/config 
      - /opt/DockerData/ts-iot-sys/java/libtaos.so:/usr/lib/libtaos.so
      - /opt/DockerData/ts-iot-sys/java/uploadPath:/uploadPath
      - /opt/DockerData/ts-iot-sys/java/logs:/logs
      - /etc/localtime:/etc/localtime
    environment:
      TZ: Asia/Shanghai
   
  webui:
    image: git.978543210.com/iot-rd/ts-iot-sys-webui:v2.2.1
    container_name: webui4tld
    ports:
      - 8999:80
    privileged: true
    restart: always
    networks:
      network:
        ipv4_address: **********
    volumes:
      - /opt/DockerData/ts-iot-sys/nginx/images:/usr/share/nginx/html/dist/images
      - /opt/DockerData/ts-iot-sys/nginx/nginx.conf:/etc/nginx/nginx.conf



  zlmedia:
    image: zlmediakit/zlmediakit:master
    container_name: zlmedia4tld
    privileged: true
    restart: always
    ports:
      - 5970:80
      - 5971:443
      - 5972:554
      - 5973:1935
      - 5974:8000
      - 30400-30500:30000-30100/udp
    expose:
      - "80"
      - "443"
      - "554"
      - "1935"
    volumes:
      - ./zlmedia/logs:/opt/media/bin/log
      - ./zlmedia/data/www:/opt/media/bin/www
      - ./zlmedia/conf/config.ini:/opt/media/conf/config.ini
      - ./zlmedia/conf/default.pem:/opt/media/bin/default.pem
    networks:
      network:
        ipv4_address: **********
        
  tdengine:
    image: 'tdengine/tdengine:*******'
    container_name: tdengine4tld
    restart: always
    hostname: tldiot
    ports:
      - 6220-6239:6030-6049
      - 6220-6239:6030-6049/udp
    volumes:
      - /opt/DockerData/ts-iot-sys/tdengine/log:/var/log/taos
      - /opt/DockerData/ts-iot-sys/tdengine/data:/var/lib/taos
      - /etc/localtime:/etc/localtime
    environment:
      TZ: Asia/Shanghai
    networks:
      network:
        ipv4_address: **********

  emqx:
    image: emqx:5.1
    container_name: emqx4tld
    ports:
      - 5975:1883
      - 5976:8083
      - 5977:8084
      - 5978:18083
    privileged: true
    restart: always
    volumes:
      - /etc/localtime:/etc/localtime
      - /opt/DockerData/ts-iot-sys/emqx/etc/emqx.conf:/opt/emqx/etc/emqx.conf
      - /opt/DockerData/ts-iot-sys/emqx/etc/acl.conf:/opt/emqx/etc/acl.conf
      - /opt/DockerData/ts-iot-sys/emqx/etc/log:/opt/emqx/log
    environment:
      SET_CONTAINER_TIMEZONE: "true"
      CONTAINER_TIMEZONE: Asia/Shanghai
    networks:
      network:
        ipv4_address: **********