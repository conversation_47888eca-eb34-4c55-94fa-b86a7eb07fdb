package com.fastbee.mq.service.impl;

import com.fastbee.common.core.mq.DeviceReportBo;
import com.fastbee.common.enums.TopicType;
import com.fastbee.common.utils.StringUtils;
import com.fastbee.common.utils.gateway.mq.TopicsUtils;
import com.fastbee.common.core.mq.message.ReportDataBo;
import com.fastbee.mq.service.IDataHandler;
import com.fastbee.mq.service.IDeviceReportMessageService;
import com.fastbee.mq.service.IMqttMessagePublish;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/2/27 14:42
 */
@Component
@Slf4j
public class DeviceOtherMsgHandler {

    @Resource
    private TopicsUtils topicsUtils;
    @Resource
    private IDataHandler dataHandler;
    @Resource
    private IMqttMessagePublish messagePublish;
    @Resource
    private IDeviceReportMessageService reportMessageService;


    /**
     * true: 使用netty搭建的mqttBroker  false: 使用emq
     */
    @Value("${server.broker.enabled}")
    private Boolean enabled;


    /**
     * 非属性消息消息处理入口
     *
     * @param bo
     */
    public void messageHandler(DeviceReportBo bo) {
        String type = "";
        String name = topicsUtils.parseTopicName(bo.getTopicName());
        if (StringUtils.isEmpty(name)) return;
        ReportDataBo data = this.buildReportData(bo);
        TopicType topicType = TopicType.getType(name);
        switch (topicType) {
            case PROPERTY_POST:
                data.setShadow(false);
                data.setType(1);
                data.setRuleEngine(true);
                reportMessageService.parseReportMsg(bo);
                break;
            case DEVICEMESSAGE_POST:
                reportMessageService.payloadMsg(bo);
                break;
            case INFO_POST:
                dataHandler.reportDevice(data);
                break;
            case NTP_POST:
                messagePublish.publishNtp(data);
                break;
            // 接收 property/get 模拟设备数据
            case PROPERTY_GET:
                messagePublish.sendSimulatorMessage(bo.getTopicName(), bo.getData());
                break;
            case PROPERTY_SET:
                messagePublish.getSimulatorInfo(bo.getTopicName(), bo.getData());
                break;
            case FUNCTION_POST:
                data.setShadow(false);
                data.setType(2);
                data.setRuleEngine(true);
                dataHandler.reportData(data);
                break;
            case EVENT_POST:
                data.setType(3);
                data.setRuleEngine(true);
                dataHandler.reportEvent(data);
                break;
            case PROPERTY_OFFLINE_POST:
                data.setShadow(true);
                data.setType(1);
                dataHandler.reportData(data);
                break;
            case FUNCTION_OFFLINE_POST:
                data.setShadow(true);
                data.setType(2);
                dataHandler.reportData(data);
                break;
        }
    }

    /**
     * 组装数据
     */
    private ReportDataBo buildReportData(DeviceReportBo bo) {
        String message = new String(bo.getData());
        log.info("收到设备信息[{}]", message);
        Long productId = topicsUtils.parseProductId(bo.getTopicName());
        ReportDataBo dataBo = new ReportDataBo();
        dataBo.setMessage(message);
        dataBo.setProductId(productId);
        dataBo.setSerialNumber(bo.getSerialNumber());
        dataBo.setRuleEngine(false);
        return dataBo;
    }

}
