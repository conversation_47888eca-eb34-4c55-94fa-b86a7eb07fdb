package com.fastbee.modbusTCP;


public class ModbusTest {
    public static void testInt() {
        ModbusDevice device = new ModbusDevice();
        device.setSlaveAddr(1);
        device.setSlaveIp("**************");
        device.setSlavePort(5020);
        device.setAddrStart(0L);
        device.setPacketLength(10);
        device.setParseType("ushort");
        device.setTimer(10L);
        device.setCode(0x03);
        device.setTimeout(1000);
        device.setTimerout(10);

        ModbusProtocolService modbusService = new ModbusProtocolService();
        modbusService.startPolling(device);
    }

    public static void testL() {
        ModbusDevice device = new ModbusDevice();
        device.setSlaveAddr(1);
        device.setSlaveIp("127.0.0.1");
        device.setSlavePort(502);
        device.setAddrStart(0L);
        device.setPacketLength(6);
        device.setParseType("float-ABCD");
        device.setTimer(5L);
        device.setCode(0x03);
        device.setTimeout(1000);
        device.setTimerout(10);

        ModbusProtocolService modbusService = new ModbusProtocolService();
        modbusService.startPolling(device);
    }

    public void testx() {
        ModbusDevice device = new ModbusDevice();
        device.setSlaveAddr(1);
        device.setSlaveIp("**************");
        device.setSlavePort(5020);
        device.setAddrStart(0L);
        device.setPacketLength(10);
        device.setParseType("ushort");
        device.setTimer(10L);
        device.setCode(0x10);
        device.setTimeout(1000);
        device.setTimerout(10);

        ModbusProtocolService modbusService = new ModbusProtocolService();
        modbusService.startPolling(device);
    }

    public static void main(String[] args) {
        //testInt();
        testL();
        //testx();
    }

}
