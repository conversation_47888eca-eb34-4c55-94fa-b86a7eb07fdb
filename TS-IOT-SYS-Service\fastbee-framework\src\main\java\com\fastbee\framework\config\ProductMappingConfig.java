package com.fastbee.framework.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@ConfigurationProperties(prefix = "product")
public class ProductMappingConfig {
    private Map<Long, List<Long>> mapping;

    public Map<Long, List<Long>> getMapping() {
        return mapping;
    }

    public void setMapping(Map<Long, List<Long>> mapping) {
        this.mapping = mapping;
    }
}
