package com.fastbee.web.controller.tool;

import com.fastbee.common.annotation.Anonymous;
import com.fastbee.device.mapper.IotDeviceMapper;
import com.fastbee.iot.domain.AlertLog;
import com.fastbee.iot.mapper.AlertLogMapper;
import com.fastbee.iot.model.DeviceRelateAlertLogVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 测试类
 * <AUTHOR>
 * @date 2023-09-13 11:42
 */
@Anonymous
@RestController
@RequestMapping("/test2")
public class TestController2 {

    @Resource
    private AlertLogMapper alertLogMapper;
    @Resource
    private IotDeviceMapper deviceMapper;

    @GetMapping("/add")
    public void add()
    {
        Set<String> deviceNumbers = new HashSet<>();
        deviceNumbers.add("D1PGLPG58K88");
        deviceNumbers.add("D1F0L7P84D8Z");
        deviceNumbers.add("D1F0L7P84D8Z_2");
        List<DeviceRelateAlertLogVO> deviceRelateAlertLogVOList = deviceMapper.selectDeviceBySerialNumbers(deviceNumbers);
        Map<String, DeviceRelateAlertLogVO> deviceRelateAlertLogVOMap = deviceRelateAlertLogVOList.stream().collect(Collectors.toMap(DeviceRelateAlertLogVO::getSerialNumber, Function.identity()));

        ArrayList<AlertLog> alertLogList = new ArrayList<>();
        for (String deviceNumber : deviceNumbers) {
            AlertLog alertLog = new AlertLog();
            alertLog.setSerialNumber(deviceNumber);
            alertLog.setAlertName("温度告警测试");
            alertLog.setAlertLevel(1L);
            alertLog.setStatus(1);
            alertLog.setProductId(1L);
            alertLog.setDetail("111");
            alertLog.setCreateTime(new Date());
            // 添加设备关联信息
            if (deviceRelateAlertLogVOMap.containsKey(deviceNumber)) {
                DeviceRelateAlertLogVO deviceRelateAlertLogVO = deviceRelateAlertLogVOMap.get(deviceNumber);
                alertLog.setDeviceName(deviceRelateAlertLogVO.getDeviceName());
                alertLog.setUserId(deviceRelateAlertLogVO.getUserId());
            }
            alertLogList.add(alertLog);
        }

        // 批量插入告警日志
        alertLogMapper.insertAlertLogBatch(alertLogList);
    }
}
