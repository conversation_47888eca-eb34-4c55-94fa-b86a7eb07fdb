package com.fastbee.device.domain;

import com.fastbee.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备导入VO类
 */
@Data
public class IotDeviceImportVO {

    /** 设备名称 */
    @ApiModelProperty("设备名称")
    @Excel(name = "设备名称")
    private String deviceName;

    /** 设备编号 */
    @ApiModelProperty("设备编号")
    @Excel(name = "设备编号")
    private String serialNumber;

    // 前端上传的是分组名称，不是 ID
    @ApiModelProperty("设备分组")
    @Excel(name = "所属单位")
    private String groupName;

    // 真正使用的字段，在导入时赋值
    private String group_id;

    // 新增字段：所有分组名称（逗号拼接）——仅用于导出 Excel 提示
    @ApiModelProperty("所有所属单位（提示用）")
    @Excel(name = "所属单位可选值（提示用）")
    private String allGroupNames;

}
