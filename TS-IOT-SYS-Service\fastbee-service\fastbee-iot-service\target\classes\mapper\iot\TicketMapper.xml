<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastbee.iot.mapper.TicketMapper">

    <resultMap type="com.fastbee.iot.domain.Ticket" id="TicketResult">
        <result property="ticketId"    column="ticket_id"    />
        <result property="alertLogId"    column="alert_log_id"    />
        <result property="title"    column="title"    />
        <result property="description"    column="description"    />
        <result property="assignedTo"    column="assigned_to"    />
        <result property="ticketType"    column="ticket_type"    />
        <result property="ticketLevel"    column="ticket_level"    />
        <result property="status"    column="status"    />
        <result property="dueDate"    column="due_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="serialNumber"    column="serial_number"    />
    </resultMap>

    <sql id="selectTicketVo">
        select ticket_id, alert_log_id, title, description, assigned_to, ticket_type, ticket_level, status,due_date,create_by, serial_number,
               create_by, create_time, update_by, update_time
        from iot_ticket
    </sql>

    <select id="selectTicketByTicketId" parameterType="Long" resultMap="TicketResult">
        <include refid="selectTicketVo"/>
        where ticket_id = #{ticketId}
    </select>

    <select id="selectTicketList" parameterType="com.fastbee.iot.domain.Ticket" resultMap="TicketResult">
        <include refid="selectTicketVo"/>
        <where>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="alertLogId != null "> and alert_log_id = #{alertLogId}</if>
            <if test="assignedTo != null and assignedTo != ''"> and assigned_to = #{assignedTo}</if>
            <if test="ticketType != null "> and ticket_type = #{ticketType}</if>
            <if test="ticketLevel != null "> and ticket_level = #{ticketLevel}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="serialNumber != null  and serialNumber != ''"> and serial_number = #{serialNumber}</if>
        </where>
        order by create_time desc
    </select>

    <insert id="insertTicket" parameterType="com.fastbee.iot.domain.Ticket" useGeneratedKeys="true" keyProperty="ticketId">
        insert into iot_ticket
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="alertLogId != null">alert_log_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="description != null and description != ''">description,</if>
            <if test="assignedTo != null and assignedTo != ''">assigned_to,</if>
            <if test="ticketType != null">ticket_type,</if>
            <if test="ticketLevel != null">ticket_level,</if>
            <if test="status != null">status,</if>
            <if test="dueDate != null">due_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="serialNumber != null  and serialNumber != ''">serial_number,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="alertLogId != null">#{alertLogId},</if>
            <if test="title != null  and title != ''">#{title},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="assignedTo != null and assignedTo != ''">#{assignedTo},</if>
            <if test="ticketType != null">#{ticketType},</if>
            <if test="ticketLevel != null">#{ticketLevel},</if>
            <if test="status != null">#{status},</if>
            <if test="dueDate != null">#{dueDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="serialNumber != null  and serialNumber != ''">#{serialNumber},</if>
         </trim>
    </insert>

    <update id="updateTicket" parameterType="com.fastbee.iot.domain.Ticket">
        update iot_ticket
        <trim prefix="SET" suffixOverrides=",">
            <if test="alertLogId != null">alert_log_id = #{alertLogId},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="assignedTo != null and assignedTo != ''">assigned_to = #{assignedTo},</if>
            <if test="ticketType != null">ticket_type = #{ticketType},</if>
            <if test="ticketLevel != null">ticket_level = #{ticketLevel},</if>
            <if test="status != null">status = #{status},</if>
            <if test="dueDate != null">due_date = #{dueDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="serialNumber != null  and serialNumber != ''">serial_number = #{serialNumber}</if>
        </trim>
        where ticket_id = #{ticketId}
    </update>

    <delete id="deleteTicketByTicketId" parameterType="Long">
        delete from iot_ticket where ticket_id = #{ticketId}
    </delete>

    <delete id="deleteTicketByTicketIds" parameterType="String">
        delete from iot_ticket where ticket_id in
        <foreach item="ticketId" collection="array" open="(" separator="," close=")">
            #{ticketId}
        </foreach>
    </delete>

</mapper>
