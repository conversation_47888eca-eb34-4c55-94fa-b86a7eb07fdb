package com.fastbee.web.controller.monitor;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import com.alibaba.fastjson2.JSON;
import com.fastbee.common.core.redis.RedisCache;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.fastbee.common.constant.CacheConstants;
import com.fastbee.common.core.domain.AjaxResult;
import com.fastbee.common.utils.StringUtils;
import com.fastbee.system.domain.SysCache;

/**
 * 缓存监控
 *
 * <AUTHOR>
 */
@Api(tags = "缓存监控")
@RestController
@RequestMapping("/monitor/cache")
public class CacheController
{
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private RedisCache redisCache;

    private final static List<SysCache> caches = new ArrayList<SysCache>();
    {
        caches.add(new SysCache(CacheConstants.LOGIN_TOKEN_KEY, "用户信息"));
        caches.add(new SysCache(CacheConstants.SYS_CONFIG_KEY, "配置信息"));
        caches.add(new SysCache(CacheConstants.SYS_DICT_KEY, "数据字典"));
        caches.add(new SysCache(CacheConstants.CAPTCHA_CODE_KEY, "验证码"));
        caches.add(new SysCache(CacheConstants.REPEAT_SUBMIT_KEY, "防重提交"));
        caches.add(new SysCache(CacheConstants.RATE_LIMIT_KEY, "限流处理"));
        caches.add(new SysCache(CacheConstants.PWD_ERR_CNT_KEY, "密码错误次数"));
        caches.add(new SysCache(CacheConstants.DEVICE_PRE_KEY, "设备中物模型值"));
        caches.add(new SysCache(CacheConstants.DEVICE_INFO, "井筒信息"));
    }

    @ApiOperation("获取缓存信息")
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping()
    public AjaxResult getInfo() throws Exception
    {
        Properties info = (Properties) redisTemplate.execute((RedisCallback<Object>) connection -> connection.info());
        Properties commandStats = (Properties) redisTemplate.execute((RedisCallback<Object>) connection -> connection.info("commandstats"));
        Object dbSize = redisTemplate.execute((RedisCallback<Object>) connection -> connection.dbSize());

        Map<String, Object> result = new HashMap<>(3);
        result.put("info", info);
        result.put("dbSize", dbSize);

        List<Map<String, String>> pieList = new ArrayList<>();
        commandStats.stringPropertyNames().forEach(key -> {
            Map<String, String> data = new HashMap<>(2);
            String property = commandStats.getProperty(key);
            data.put("name", StringUtils.removeStart(key, "cmdstat_"));
            data.put("value", StringUtils.substringBetween(property, "calls=", ",usec"));
            pieList.add(data);
        });
        result.put("commandStats", pieList);
        return AjaxResult.success(result);
    }

    @ApiOperation("缓存列表")
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping("/getNames")
    public AjaxResult cache()
    {
        return AjaxResult.success(caches);
    }

    @ApiOperation("键名列表")
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping("/getKeys/{cacheName}")
    public AjaxResult getCacheKeys(@PathVariable String cacheName)
    {
        Set<String> cacheKeys = redisTemplate.keys(cacheName + "*");
        return AjaxResult.success(cacheKeys);
    }

    @ApiOperation("缓存内容")
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping("/getValue/{cacheName}/{cacheKey}")
    public AjaxResult getCacheValue(@PathVariable String cacheName, @PathVariable String cacheKey)
    {
        String cacheValue = null;
        try {

                    // 获取键的类型
            String keyType =  (String) redisCache.getKeyType(cacheKey);
            if (keyType == null || keyType.equalsIgnoreCase("none")) {
            }
            switch (keyType.toLowerCase()) {
                case "string":
                    // 如果是字符串类型
                    cacheValue = redisTemplate.opsForValue().get(cacheKey);
                case "hash":
                    // 如果是哈希类型
                    Map<Object, Object> cacheMapValue  = redisTemplate.opsForHash().entries(cacheKey); // 替换为实际字段名
                    if (cacheMapValue != null) {
                        cacheValue = JSON.toJSONString(cacheMapValue);
                    }
                case "list":
                    // 如果是列表类型
                    List<String> listValues = redisTemplate.opsForList().range(cacheKey, 0, -1); // 获取整个列表
                    if (listValues != null || !listValues.isEmpty()) {
                        cacheValue = JSON.toJSONString(listValues);
                    }
                case "set":
                    // 如果是集合类型
                    Set<String> setValues = redisTemplate.opsForSet().members(cacheKey); // 获取集合中的所有元素
                    if (setValues != null || !setValues.isEmpty()) {
                        cacheValue = JSON.toJSONString(setValues);
                    }
                case "zset":
                    // 如果是有序集合类型
                    Set<String> zsetValues = redisTemplate.opsForZSet().range(cacheKey, 0, -1); // 获取有序集合中的所有元素
                    if (zsetValues != null || !zsetValues.isEmpty()) {
                        cacheValue = JSON.toJSONString(zsetValues);
                    }
                default:
            }
        } catch (Exception e) {

        }
        SysCache sysCache = new SysCache(cacheName, cacheKey, cacheValue);
        return AjaxResult.success(sysCache);
    }

    @ApiOperation("清理缓存名称")
    @PreAuthorize("@ss.hasPermi('monitor:cache:remove')")
    @DeleteMapping("/clearCacheName/{cacheName}")
    public AjaxResult clearCacheName(@PathVariable String cacheName)
    {
        Collection<String> cacheKeys = redisTemplate.keys(cacheName + "*");
        redisTemplate.delete(cacheKeys);
        return AjaxResult.success();
    }

    @ApiOperation("清理缓存键名")
    @PreAuthorize("@ss.hasPermi('monitor:cache:remove')")
    @DeleteMapping("/clearCacheKey/{cacheKey}")
    public AjaxResult clearCacheKey(@PathVariable String cacheKey)
    {
        redisTemplate.delete(cacheKey);
        return AjaxResult.success();
    }

    @ApiOperation("清理所有缓存内容")
    @PreAuthorize("@ss.hasPermi('monitor:cache:remove')")
    @DeleteMapping("/clearCacheAll")
    public AjaxResult clearCacheAll()
    {
        Collection<String> cacheKeys = redisTemplate.keys("*");
        redisTemplate.delete(cacheKeys);
        return AjaxResult.success();
    }


}
