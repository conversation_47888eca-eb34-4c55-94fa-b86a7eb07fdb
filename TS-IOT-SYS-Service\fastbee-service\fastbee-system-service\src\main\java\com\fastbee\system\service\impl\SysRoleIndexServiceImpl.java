package com.fastbee.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fastbee.common.core.domain.entity.SysRoleIndex;
import com.fastbee.common.core.domain.entity.SysUser;
import com.fastbee.common.core.redis.RedisCache;
import com.fastbee.common.enums.DefIndexConst;
import com.fastbee.common.enums.RoleIndexConfigEnum;
import com.fastbee.common.utils.DateUtils;
import com.fastbee.system.mapper.SysRoleIndexMapper;
import com.fastbee.system.service.ISysRoleIndexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;

import static com.fastbee.common.utils.SecurityUtils.getLoginUser;

/**
 * 角色首页配置
 */
@Service
public class SysRoleIndexServiceImpl extends ServiceImpl<SysRoleIndexMapper, SysRoleIndex> implements ISysRoleIndexService {

    @Autowired
    private SysRoleIndexMapper sysRoleIndexMapper;

    @Autowired
    private RedisCache redisCache;

    @Override
    public List<SysRoleIndex> selectRoleIndexList(SysRoleIndex sysRoleIndex) {
        return sysRoleIndexMapper.selectRoleIndexList(sysRoleIndex);
    }

    @Override
    public SysRoleIndex selectRoleIndexById(String id) {
        return sysRoleIndexMapper.selectRoleIndexById(id);
    }

    @Override
    public int insertRoleIndex(SysRoleIndex sysRoleIndex) {
        sysRoleIndex.setId(UUID.randomUUID().toString().replace("-", ""));
        sysRoleIndex.setCreateTime(DateUtils.getNowDate());
        SysUser sysUser = getLoginUser().getUser();
        sysRoleIndex.setCreateBy(sysUser.getUserName());
        return sysRoleIndexMapper.insertRoleIndex(sysRoleIndex);
    }

    @Override
    public int updateRoleIndex(SysRoleIndex sysRoleIndex) {
        sysRoleIndex.setUpdateTime(DateUtils.getNowDate());
        SysUser sysUser = getLoginUser().getUser();
        sysRoleIndex.setUpdateBy(sysUser.getUserName());
        return sysRoleIndexMapper.updateRoleIndex(sysRoleIndex);
    }

    @Override
    public int deleteRoleIndexByIds(String[] ids) {
        return sysRoleIndexMapper.deleteRoleIndexByIds(ids);
    }

    @Override
    public int deleteRoleIndexById(String id) {
        return sysRoleIndexMapper.deleteRoleIndexById(id);
    }

    @Override
    public SysRoleIndex queryDefaultIndex() {
        try {
            // 尝试从缓存获取
            String cacheKey = DefIndexConst.CACHE_KEY  + DefIndexConst.DEF_INDEX_ALL;
            SysRoleIndex cached = redisCache.getCacheObject(cacheKey);
            if (cached != null) {
                return cached;
            }
        } catch (Exception e) {
            // 如果缓存反序列化失败，清理缓存
            this.cleanDefaultIndexCache();
        }

        // 从数据库查询
        SysRoleIndex query = new SysRoleIndex();
        query.setRoleCode(DefIndexConst.DEF_INDEX_ALL);
        List<SysRoleIndex> list = sysRoleIndexMapper.selectRoleIndexList(query);

        SysRoleIndex result;
        if (CollectionUtils.isEmpty(list)) {
            result = this.initDefaultIndex();
        } else {
            result = list.get(0);
        }

        // 重新缓存
        String cacheKey = DefIndexConst.CACHE_KEY + DefIndexConst.DEF_INDEX_ALL;
        redisCache.setCacheObject(cacheKey, result);

        return result;
    }

    @Override
    public boolean updateDefaultIndex(SysRoleIndex sysRoleIndex) {
        SysRoleIndex query = new SysRoleIndex();
        query.setRoleCode(DefIndexConst.DEF_INDEX_ALL);
        List<SysRoleIndex> list = sysRoleIndexMapper.selectRoleIndexList(query);


        int result = 0;
        String url = sysRoleIndex.getUrl();
        String component = sysRoleIndex.getComponent();
        Boolean isRoute = sysRoleIndex.getIsRoute();

        if (CollectionUtils.isEmpty(list)) {
            // 新增
            SysRoleIndex entity = this.newDefIndexConfig(url, component, isRoute);
            result = this.insertRoleIndex(entity);
        } else {
            // 更新
            SysRoleIndex entity = list.get(0);
            entity.setUrl(url);
            entity.setComponent(component);
            entity.setIsRoute(isRoute);
            result = this.updateRoleIndex(entity);
        }

        if (result > 0) {
            this.cleanDefaultIndexCache();
        }
        return result > 0;
    }

    @Override
    public SysRoleIndex initDefaultIndex() {
        return this.newDefIndexConfig(DefIndexConst.DEF_INDEX_URL, DefIndexConst.DEF_INDEX_COMPONENT, true);
    }

    @Override
    public SysRoleIndex getIndexByRoles(List<String> roles) {
        if (CollectionUtils.isEmpty(roles)) {
            return queryDefaultIndex();
        }

        // 查询角色首页配置
        SysRoleIndex query = new SysRoleIndex();
        query.setRoleCodes(roles); // 需要在实体类中添加roleCodes字段用于查询
        query.setStatus("1");
        List<SysRoleIndex> roleIndexList = sysRoleIndexMapper.selectRoleIndexByRoles(query);

        if (CollectionUtils.isNotEmpty(roleIndexList)) {
            // 按优先级排序，返回第一个
            roleIndexList.sort(Comparator.comparing(SysRoleIndex::getPriority, Comparator.nullsLast(Integer::compareTo)));
            return roleIndexList.get(0);
        }

        // 如果没有配置，使用枚举配置
        String componentUrl = RoleIndexConfigEnum.getIndexByRoles(roles);
        SysRoleIndex roleIndex = new SysRoleIndex(componentUrl);
        roleIndex.setUrl(DefIndexConst.DEF_INDEX_URL);
        roleIndex.setIsRoute(true);

        return roleIndex;
    }

    private SysRoleIndex newDefIndexConfig(String indexUrl, String indexComponent, boolean isRoute) {
        SysRoleIndex entity = new SysRoleIndex();
        entity.setRoleCode(DefIndexConst.DEF_INDEX_ALL);
        entity.setUrl(indexUrl);
        entity.setComponent(indexComponent);
        entity.setIsRoute(isRoute);
        entity.setStatus("1");
        entity.setPriority(1);
        return entity;
    }

    @Override
    public void cleanDefaultIndexCache() {
        redisCache.deleteObject(DefIndexConst.CACHE_KEY + DefIndexConst.DEF_INDEX_ALL);
    }
}