package com.fastbee.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * topic类型
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TopicType {

    /**
     * @param     type  0:标记是订阅主题  1:标记是发布属性
     * @param     order 排序
     * @param     topicSuffix topic后缀
     * @param     msg  描述信息
     */

    /*** 通用设备上报主题（平台订阅） ***/
    PROPERTY_POST(0,1,"/property/post", "订阅属性"),
    EVENT_POST(0,2,"/event/post", "订阅事件"),
    FUNCTION_POST(0,3,"/function/post", "订阅功能"),
    INFO_POST(0,4,"/info/post","订阅设备信息"),
    NTP_POST(0,5,"/ntp/post","订阅时钟同步"),
    PROPERTY_OFFLINE_POST(0,6,"/property-offline/post","订阅属性(影子模式)"),
    FUNCTION_OFFLINE_POST(0,7,"/function-offline/post","订阅功能(影子模式)"),
    SERVICE_INVOKE_REPLY(0,8,"/service/reply", "订阅功能调用返回结果"),
    FIRMWARE_UPGRADE_REPLY(0,9,"/upgrade/reply", "订阅设备OTA升级结果"),
    PROP_ERAD_REPLY(0,10,"/property/getreply","订阅设备属性读取响应"),
    MESSAGE_POST(0,26,"/message/post","订阅设备上报消息"),
    DEVICEMESSAGE_POST(0,27,"/devicemessage/post", "订阅井口消息"),

    HN3S1_HN9V23C(5, 1,"/HN3S1/HN9V23C","订阅海南三区1号站"),
    HN3S1_HN11V27(5, 2,"/HN3S1/HN11V27","订阅海南三区1号站"),
    HN3S1_HN12V13(5, 3,"/HN3S1/HN12V13","订阅海南三区1号站"),
    HN3S1_HN13V23(5, 4,"/HN3S1/HN13V23","订阅海南三区1号站"),
    HN3S1_HN13V25(5, 5,"/HN3S1/HN13V25","订阅海南三区1号站"),
    HN3S1_HN15V23(5, 7,"/HN3S1/HN15V23","订阅海南三区1号站"),
    HN3S1_HN15V27(5, 8,"/HN3S1/HN15V27","订阅海南三区1号站"),
    HN3S1_HN15V29(5, 9,"/HN3S1/HN15V29","订阅海南三区1号站"),
    HN3S1_HN15V31(5, 10,"/HN3S1/HN15V31","订阅海南三区1号站"),
    HN3S1_HN17V29(5, 11,"/HN3S1/HN17V29","订阅海南三区1号站"),

    HN3S2_HN16V18(5, 12, "/HN3S2/HN16V18", "订阅海南三区2号站"),
    HN3S2_HN17V5(5, 13, "/HN3S2/HN17V5", "订阅海南三区2号站"),
    HN3S2_HN19V13C(5, 14, "/HN3S2/HN19V13C", "订阅海南三区2号站"),
    HN3S2_HN19V15(5, 15, "/HN3S2/HN19V15", "订阅海南三区2号站"),
    HN3S2_HN19V21(5, 16, "/HN3S2/HN19V21", "订阅海南三区2号站"),
    HN3S2_HN19V23(5, 17, "/HN3S2/HN19V23", "订阅海南三区2号站"),
    HN3S2_HN21V7(5, 18, "/HN3S2/HN21V7", "订阅海南三区2号站"),
    HN3S2_HN21V11(5, 19, "/HN3S2/HN21V11", "订阅海南三区2号站"),
    HN3S2_HN21V15(5, 20, "/HN3S2/HN21V15", "订阅海南三区2号站"),
    HN3S2_HN21V17(5, 21, "/HN3S2/HN21V17", "订阅海南三区2号站"),
    HN3S2_HN21V25(5, 22, "/HN3S2/HN21V25", "订阅海南三区2号站"),
    HN3S2_HN23V7(5, 23, "/HN3S2/HN23V7", "订阅海南三区2号站"),
    HN3S2_HN23V9(5, 24, "/HN3S2/HN23V9", "订阅海南三区2号站"),
    HN3S2_HN23V11(5, 25, "/HN3S2/HN23V11", "订阅海南三区2号站"),
    HN3S2_HN23V15(5, 26, "/HN3S2/HN23V15", "订阅海南三区2号站"),
    HN3S2_HN23V19(5, 27, "/HN3S2/HN23V19", "订阅海南三区2号站"),
    HN3S2_HN25V7(5, 28, "/HN3S2/HN25V7", "订阅海南三区2号站"),
    HN3S2_HN25V13(5, 29, "/HN3S2/HN25V13", "订阅海南三区2号站"),
    HN3S2_HN25V15(5, 30, "/HN3S2/HN25V15", "订阅海南三区2号站"),
    HN3S2_HN25V17(5, 31, "/HN3S2/HN25V17", "订阅海南三区2号站"),
    HN3S2_HN25V19(5, 32, "/HN3S2/HN25V19", "订阅海南三区2号站"),
    HN3S2_HN25V21C(5, 33, "/HN3S2/HN25V21C", "订阅海南三区2号站"),
    HN3S2_HN27V7(5, 34, "/HN3S2/HN27V7", "订阅海南三区2号站"),
    HN3S2_HN27V9(5, 35, "/HN3S2/HN27V9", "订阅海南三区2号站"),
    HN3S2_HN27V13(5, 36, "/HN3S2/HN27V13", "订阅海南三区2号站"),
    HN3S2_HN27V15(5, 37, "/HN3S2/HN27V15", "订阅海南三区2号站"),
    HN3S2_HN27V17(5, 38, "/HN3S2/HN27V17", "订阅海南三区2号站"),
    HN3S2_HN29V11(5, 39, "/HN3S2/HN29V11", "订阅海南三区2号站"),
    HN3S2_HN33V5(5, 40, "/HN3S2/HN33V5", "订阅海南三区2号站"),
    HN3S2_HN33V9(5, 41, "/HN3S2/HN33V9", "订阅海南三区2号站"),
    HN3S2_HNQ1C(5, 42, "/HN3S2/HNQ1C", "订阅海南三区2号站"),
    HN3S2_HN19V7(5, 43, "/HN3S2/HN19V7", "订阅海南三区2号站"),
    HN3S2_HN21V9(5, 6, "/HN3S2/HN21V9", "订阅海南三区2号站"),

    HN3S3_HN7V5(5, 44, "/HN3S3/HN7V5", "订阅海南三区3号站"),
    HN3S3_HN8V4(5, 45, "/HN3S3/HN8V4", "订阅海南三区3号站"),
    HN3S3_HN9V3(5, 46, "/HN3S3/HN9V3", "订阅海南三区3号站"),
    HN3S3_HN9V5(5, 47, "/HN3S3/HN9V5", "订阅海南三区3号站"),
    HN3S3_HN10V4(5, 48, "/HN3S3/HN10V4", "订阅海南三区3号站"),
    HN3S3_HN10V8(5, 49, "/HN3S3/HN10V8", "订阅海南三区3号站"),
    HN3S3_HN11V5(5, 50, "/HN3S3/HN11V5", "订阅海南三区3号站"),
    HN3S3_HN11V7(5, 51, "/HN3S3/HN11V7", "订阅海南三区3号站"),
    HN3S3_HN11V21(5, 52, "/HN3S3/HN11V21", "订阅海南三区3号站"),
    HN3S3_HN12V3(5, 53, "/HN3S3/HN12V3", "订阅海南三区3号站"),
    HN3S3_HN13V5(5, 54, "/HN3S3/HN13V5", "订阅海南三区3号站"),
    HN3S3_HN13V7(5, 55, "/HN3S3/HN13V7", "订阅海南三区3号站"),
    HN3S3_HN13V9C(5, 56, "/HN3S3/HN13V9C", "订阅海南三区3号站"),
    HN3S3_HN13V13C(5, 57, "/HN3S3/HN13V13C", "订阅海南三区3号站"),
    HN3S3_HN13V19(5, 58, "/HN3S3/HN13V19", "订阅海南三区3号站"),
    HN3S3_HN13V21(5, 59, "/HN3S3/HN13V21", "订阅海南三区3号站"),
    HN3S3_HN15V5(5, 60, "/HN3S3/HN15V5", "订阅海南三区3号站"),
    HN3S3_HN15V7(5, 61, "/HN3S3/HN15V7", "订阅海南三区3号站"),
    HN3S3_HN15V9(5, 62, "/HN3S3/HN15V9", "订阅海南三区3号站"),
    HN3S3_HN15V11C(5, 63, "/HN3S3/HN15V11C", "订阅海南三区3号站"),
    HN3S3_HN15V13(5, 64, "/HN3S3/HN15V13", "订阅海南三区3号站"),
    HN3S3_HN15V17C(5, 65, "/HN3S3/HN15V17C", "订阅海南三区3号站"),
    HN3S3_HN15V19C(5, 66, "/HN3S3/HN15V19C", "订阅海南三区3号站"),
    HN3S3_HN17V11(5, 67, "/HN3S3/HN17V11", "订阅海南三区3号站"),
    HN3S3_HN17V21(5, 68, "/HN3S3/HN17V21", "订阅海南三区3号站"),
    HN3S3_HN18V14(5, 69, "/HN3S3/HN18V14", "订阅海南三区3号站"),
    HN3S3_HN19V19C(5, 70, "/HN3S3/HN19V19C", "订阅海南三区3号站"),
    HN3S3_HN21G21(5, 71, "/HN3S3/HN21G21", "订阅海南三区3号站"),
    HN3S3_HN12V10C(5, 83, "/HN3S3/HN12V10C", "订阅海南三区3号站"),
    HN3S3_HN11V13(5, 84, "/HN3S3/HN11V13", "订阅海南三区3号站"),

    HN3S4_HNQ2(5, 72, "/HN3S4/HNQ2", "订阅海南三区4号站"),
    HN3S4_HNQ3(5, 73, "/HN3S4/HNQ3", "订阅海南三区4号站"),
    HN3S4_HNQ5C(5, 74, "/HN3S4/HNQ5C", "订阅海南三区4号站"),
    HN3S4_HNQ6(5, 75, "/HN3S4/HNQ6", "订阅海南三区4号站"),
    HN3S4_HN7V3C(5, 76, "/HN3S4/HN7V3C", "订阅海南三区4号站"),
    HN3S4_HN7V7C(5, 77, "/HN3S4/HN7V7C", "订阅海南三区4号站"),
    HN3S4_HN9V9(5, 78, "/HN3S4/HN9V9", "订阅海南三区4号站"),
    HN3S4_HN01V3C(5, 79, "/HN3S4/HN01V3C", "订阅海南三区4号站"),
    HN3S4_HN1V3(5, 80, "/HN3S4/HN1V3", "订阅海南三区4号站"),
    HN3S4_HN5V3(5, 81, "/HN3S4/HN5V3", "订阅海南三区4号站"),
    HN3S4_HN5V7(5, 82, "/HN3S4/HN5V7", "订阅海南三区4号站"),

    //水井
    HN3S1_HN13V27(5, 102,"/HN3S1/HN13V27","订阅海南三区1号站"),
    HN3S1_HN11V23(5, 85,"/HN3S1/HN11V23","订阅海南三区1号站"),
    HN3S1_HN15V25(5, 103,"/HN3S1/HN15V25","订阅海南三区1号站"),

    HN3S2_HN23V17(5, 86, "/HN3S2/HN23V17", "订阅海南三区2号站"),
    HN3S2_HN19V5(5, 87, "/HN3S2/HN19V5", "订阅海南三区2号站"),
    HN3S2_HN23V13(5, 88, "/HN3S2/HN23V13", "订阅海南三区2号站"),
    HN3S2_HN19V25(5, 89, "/HN3S2/HN19V25", "订阅海南三区2号站"),
    HN3S2_HN25V25(5, 90, "/HN3S2/HN25V25", "订阅海南三区2号站"),
    HN3S2_HN23V23(5, 91, "/HN3S2/HN23V23", "订阅海南三区2号站"),
    HN3S2_HN25V11(5, 92, "/HN3S2/HN25V11", "订阅海南三区2号站"),

    HN3S3_HN11V11(5, 93, "/HN3S3/HN11V11", "订阅海南三区3号站"),
    HN3S3_HN13V17(5, 94, "/HN3S3/HN13V17", "订阅海南三区3号站"),
    HN3S3_HN14V8(5, 95, "/HN3S3/HN14V8", "订阅海南三区3号站"),
    HN3S3_HN15V21(5, 96, "/HN3S3/HN15V21", "订阅海南三区3号站"),
    HN3S3_HN17V19(5, 97, "/HN3S3/HN17V19", "订阅海南三区3号站"),
    HN3S3_HN18V12(5, 98, "/HN3S3/HN18V12", "订阅海南三区3号站"),
    HN3S3_HN21V13(5, 99, "/HN3S3/HN21V13", "订阅海南三区3号站"),
    HN3S3_HN17G9(5, 100, "/HN3S3/HN17G9", "订阅海南三区3号站"),
    HN3S3_HN3(5, 101, "/HN3S3/HN3", "订阅海南三区3号站"),

    HN3S4_HN3V3(5, 104, "/HN3S4/HN3V3", "订阅海南三区4号站"),
    HN3S4_HN5V9(5, 105, "/HN3S4/HN5V9", "订阅海南三区4号站"),
    HN3S4_HN602(5, 106, "/HN3S4/HN602", "订阅海南三区4号站"),
    HN3S4_HN5V5(5, 107, "/HN3S4/HN5V5", "订阅海南三区4号站"),

    /*** 通用设备订阅主题（平台下发）***/
    FUNCTION_GET(1,17,"/function/get", "发布功能"),
    PROPERTY_GET(1,12,"/property/get" ,"发布设备属性读取"),
    PROPERTY_SET(1,13,"/property/set" ,"设置设备属性读取"),
    FIRMWARE_SET(1,14, "/upgrade/set","发布OTA升级"),
    STATUS_POST(1,11,"/status/post","发布状态"),
    NTP_GET(1,15,"/ntp/get","发布时钟同步"),
    INFO_GET(1,18,"/info/get","发布设备信息"),


    /*** 视频监控设备转协议发布 ***/
    DEV_INFO_POST(3,19,"/info/post","设备端发布设备信息"),
    DEV_EVENT_POST(3,20,"/event/post","设备端发布事件"),
    DEV_FUNCTION_POST(3,21,"/function/post", "设备端发布功能"),
    DEV_PROPERTY_POST(3,22,"/property/post", "设备端发布属性"),


    /*** webSocket转发前端使用  ***/
    WS_SERVICE_INVOKE(2,16,"/ws/service", "WS服务调用"),
    WS_LOG_INVOKE(2,17,"/ws/log","ws下发指令日志"),
    MONITOR_POST(2,18,"/monitor/post", "发布实时监测数据"),



    /*** 模拟设备使用 ***/
    PROPERTY_GET_SIMULATE(4,23,"/property/get/simulate" ,"发布属性读取"),
    PROPERTY_SET_SIMULATE(4,13, "/property/set/simulate","发布属性写入"),
    WS_SERVICE_INVOKE_SIMULATE(2,24,"/ws/post/simulate", "模拟设备WS推送"),
    PROPERTY_POST_SIMULATE(2,25,"/property/simulate/post", "订阅属性");

    Integer type;
    Integer order;
    String topicSuffix;
    String msg;

    public static TopicType getType(String topicSuffix) {
        for (TopicType value : TopicType.values()) {
            if (value.topicSuffix.equals(topicSuffix)) {
                return value;
            }
        }
        return TopicType.PROPERTY_POST;
    }


}
