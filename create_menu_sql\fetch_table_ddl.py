#!/usr/bin/env python
# -*- coding: utf-8 -*-

import mysql.connector
import os

def fetch_table_ddl():
    # 数据库连接参数 - 请修改为您的实际连接信息
    config = {
        'host': 'localhost',  # 数据库主机名
        'port': 5981,         # 端口号
        'user': 'root',  # 用户名
        'password': '123456',  # 密码
        'database': 'fastbee5'  # 数据库名
    }
    
    # 输入和输出文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    show_file = os.path.join(current_dir, 'show')
    output_file = os.path.join(current_dir, 'all_tables_ddl.txt')
    
    try:
        # 读取SQL语句
        with open(show_file, 'r', encoding='utf-8') as f:
            sql_statements = f.readlines()
        
        # 连接数据库
        print("正在连接到MySQL数据库...")
        cnx = mysql.connector.connect(**config)
        cursor = cnx.cursor()
        
        # 打开输出文件
        with open(output_file, 'w', encoding='utf-8') as out_file:
            # 处理每一条SQL语句
            for i, sql in enumerate(sql_statements, 1):
                sql = sql.strip()
                if not sql:
                    continue
                
                print(f"正在执行第 {i}/{len(sql_statements)} 条SQL: {sql}")
                
                try:
                    # 执行SQL语句
                    cursor.execute(sql)
                    
                    # 获取结果
                    for result in cursor:
                        # SHOW CREATE TABLE返回格式为 (表名, 建表语句)
                        table_name = result[0]
                        create_statement = result[1]
                        
                        # 写入分隔符和表名
                        out_file.write(f"\n-- --------------------------------------------------------\n")
                        out_file.write(f"-- 表结构: {table_name}\n")
                        out_file.write(f"-- --------------------------------------------------------\n\n")
                        
                        # 写入建表语句
                        out_file.write(f"{create_statement};\n\n")
                
                except mysql.connector.Error as err:
                    print(f"执行SQL出错: {err}")
                    out_file.write(f"-- Error executing SQL for {sql}: {err}\n\n")
        
        print(f"\n完成! 所有表的DDL已保存到: {output_file}")
    
    except mysql.connector.Error as err:
        print(f"数据库连接错误: {err}")
    except FileNotFoundError:
        print(f"找不到文件: {show_file}")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        # 关闭数据库连接
        if 'cursor' in locals():
            cursor.close()
        if 'cnx' in locals():
            cnx.close()

if __name__ == "__main__":
    fetch_table_ddl() 