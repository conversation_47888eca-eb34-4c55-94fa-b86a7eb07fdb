<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fastbee</artifactId>
        <groupId>com.fastbee</groupId>
        <version>3.8.5</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>fastbee-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>
        <!-- swagger3-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>

        <!-- 防止进入swagger页面报类型转换错误，排除3.0.0中的引用，手动增加1.6.2版本 -->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.6.2</version>
        </dependency>

         <!-- Mysql驱动包 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- postgresql驱动包 -->
<!--        <dependency>-->
<!--            <groupId>org.postgresql</groupId>-->
<!--            <artifactId>postgresql</artifactId>-->
<!--            <version>42.2.23</version>-->
<!--        </dependency>-->

        <!-- 核心模块-->
        <dependency>
            <groupId>com.fastbee</groupId>
            <artifactId>fastbee-framework</artifactId>
        </dependency>

        <!-- 定时任务-->
        <dependency>
            <groupId>com.fastbee</groupId>
            <artifactId>fastbee-quartz</artifactId>
        </dependency>

        <!-- 代码生成-->
        <dependency>
            <groupId>com.fastbee</groupId>
            <artifactId>fastbee-generator</artifactId>
        </dependency>

        <!-- 系统服务模块-->
        <dependency>
            <groupId>com.fastbee</groupId>
            <artifactId>fastbee-system-service</artifactId>
        </dependency>

        <!-- controller API模块-->
        <dependency>
            <groupId>com.fastbee</groupId>
            <artifactId>fastbee-open-api</artifactId>
        </dependency>
        <!--服务集成启动模块-->
        <dependency>
            <groupId>com.fastbee</groupId>
            <artifactId>boot-strap</artifactId>
        </dependency>
        <!--网关集成启动模块-->
        <dependency>
            <groupId>com.fastbee</groupId>
            <artifactId>gateway-boot</artifactId>
        </dependency>
        <!--OSS文件存储模块-->
        <dependency>
            <groupId>com.fastbee</groupId>
            <artifactId>fastbee-oss</artifactId>
        </dependency>
        <!--SIP视频模块-->
        <dependency>
            <groupId>com.fastbee</groupId>
            <artifactId>sip-server</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>liteflow-core</artifactId>
            <version>2.12.2</version>
        </dependency>

        <!-- 通知配置模块 -->
        <dependency>
            <groupId>com.fastbee</groupId>
            <artifactId>fastbee-notify-web</artifactId>
        </dependency>
        <!-- 通知api模块 -->
        <dependency>
            <groupId>com.fastbee</groupId>
            <artifactId>fastbee-notify-core</artifactId>
        </dependency>

        <!-- oauth2.0授权模块 -->
        <dependency>
            <groupId>com.fastbee</groupId>
            <artifactId>fastbee-oauth</artifactId>
        </dependency>

        <!-- 组态模块 -->
        <dependency>
            <groupId>com.fastbee</groupId>
            <artifactId>fastbee-scada</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.1.RELEASE</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
           </plugin>
        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>

</project>
