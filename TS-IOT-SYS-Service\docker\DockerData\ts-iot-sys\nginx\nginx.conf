#user  nobody;
worker_processes 1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;
events {
    worker_connections 1024;
}

http {
    include mime.types;
    default_type application/octet-stream;
    error_log stderr;           # 强制错误日志输出到标准错误
    access_log /dev/stdout;     # 强制访问日志输出到标准输出

    root /usr/share/nginx/html/dist; 

    server {
        listen 80;
        
        location / {
            root /usr/share/nginx/html/dist;
            try_files $uri $uri/ /index.html;
            index index.html;

            # Enable CORS
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';

            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain charset=UTF-8';
                add_header 'Content-Length' 0;
                return 204;
            }
        }

        location /assets/css/images/ {
	        alias /usr/share/nginx/html/dist/images/;
	        expires 30d;  
	        add_header Cache-Control "public";
	    }

        location /prod-api/ {
            proxy_pass http://**********:8080/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
