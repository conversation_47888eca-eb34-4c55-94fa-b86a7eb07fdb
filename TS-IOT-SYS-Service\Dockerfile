FROM git.978543210.com/iot-rd/openjdk:8-jdk-alpine AS builder

# 切换为阿里云 Alpine v3.10 源，加速 apk 安装
RUN echo "http://mirrors.aliyun.com/alpine/v3.10/main/" > /etc/apk/repositories && \
    echo "http://mirrors.aliyun.com/alpine/v3.10/community/" >> /etc/apk/repositories

# 安装 Maven 和一些构建所需的依赖
RUN apk update && apk add --no-cache maven bash

# 显式创建目录
RUN mkdir -p /app

# 设置工作目录
WORKDIR /app

# 复制 pom.xml 和 src 目录
COPY pom.xml .
COPY fastbee-admin ./fastbee-admin
COPY fastbee-common ./fastbee-common
COPY fastbee-framework ./fastbee-framework
COPY fastbee-gateway ./fastbee-gateway
COPY fastbee-notify ./fastbee-notify
COPY fastbee-open-api ./fastbee-open-api
COPY fastbee-plugs ./fastbee-plugs
COPY fastbee-protocol ./fastbee-protocol
COPY fastbee-scada ./fastbee-scada
COPY fastbee-server ./fastbee-server
COPY fastbee-service ./fastbee-service

# 构建 Spring Boot 应用程序
RUN mvn clean package -DskipTests

FROM git.978543210.com/iot-rd/openjdk:8-jre-alpine

# 切换为阿里云 Alpine v3.10 源，加速 apk 安装
RUN echo "http://mirrors.aliyun.com/alpine/v3.10/main/" > /etc/apk/repositories && \
    echo "http://mirrors.aliyun.com/alpine/v3.10/community/" >> /etc/apk/repositories

RUN apk update && apk upgrade && \
    apk add  --no-cache \
    ttf-dejavu fontconfig udev ca-certificates \
    && rm -rf /var/cache/apk/*

# 设置工作目录
WORKDIR /app

# 从构建阶段复制 jar 文件和配置文件
COPY --from=builder /app/fastbee-admin/target/*.jar app.jar

# 将配置文件复制到容器中的 config 目录
COPY --from=builder /app/fastbee-admin/target/classes/ ./config/

EXPOSE 8080

# 启动应用程序，通过 -Dspring.config.location 参数指定配置文件路径
CMD ["java", "-jar", "app.jar", "--spring.config.location=classpath:/,file:./config/"]
