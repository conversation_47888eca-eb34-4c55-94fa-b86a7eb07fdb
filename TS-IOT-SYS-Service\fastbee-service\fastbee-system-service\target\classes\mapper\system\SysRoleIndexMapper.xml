<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastbee.system.mapper.SysRoleIndexMapper">

    <resultMap type="SysRoleIndex" id="SysRoleIndexResult">
        <result property="id" column="id"/>
        <result property="roleCode" column="role_code"/>
        <result property="url" column="url"/>
        <result property="component" column="component"/>
        <result property="isRoute" column="is_route"/>
        <result property="priority" column="priority"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="selectSysRoleIndexVo">
        select id, role_code, url, component, is_route, priority, status, create_time, create_by, update_time, update_by
        from sys_role_index
    </sql>

    <select id="selectRoleIndexList" parameterType="SysRoleIndex" resultMap="SysRoleIndexResult">
        <include refid="selectSysRoleIndexVo"/>
        <where>
            <if test="roleCode != null and roleCode != ''">
                AND role_code like concat('%', #{roleCode}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        order by priority asc, create_time desc
    </select>

    <select id="selectRoleIndexByRoles" parameterType="SysRoleIndex" resultMap="SysRoleIndexResult">
        <include refid="selectSysRoleIndexVo"/>
        <where>
            <if test="roleCodes != null and roleCodes.size() > 0">
                AND role_code in
                <foreach item="roleCode" collection="roleCodes" open="(" separator="," close=")">
                    #{roleCode}
                </foreach>
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        order by priority asc
    </select>

    <select id="selectRoleIndexById" parameterType="String" resultMap="SysRoleIndexResult">
        <include refid="selectSysRoleIndexVo"/>
        where id = #{id}
    </select>

    <insert id="insertRoleIndex" parameterType="SysRoleIndex">
        insert into sys_role_index
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="roleCode != null">role_code,</if>
            <if test="url != null">url,</if>
            <if test="component != null">component,</if>
            <if test="isRoute != null">is_route,</if>
            <if test="priority != null">priority,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="roleCode != null">#{roleCode},</if>
            <if test="url != null">#{url},</if>
            <if test="component != null">#{component},</if>
            <if test="isRoute != null">#{isRoute},</if>
            <if test="priority != null">#{priority},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
        </trim>
    </insert>

    <update id="updateRoleIndex" parameterType="SysRoleIndex">
        update sys_role_index
        <trim prefix="SET" suffixOverrides=",">
            <if test="roleCode != null">role_code = #{roleCode},</if>
            <if test="url != null">url = #{url},</if>
            <if test="component != null">component = #{component},</if>
            <if test="isRoute != null">is_route = #{isRoute},</if>
            <if test="priority != null">priority = #{priority},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRoleIndexById" parameterType="String">
        delete from sys_role_index where id = #{id}
    </delete>

    <delete id="deleteRoleIndexByIds" parameterType="String">
        delete from sys_role_index where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>