package com.fastbee.web.controller.system;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fastbee.common.core.domain.AjaxResult;
import com.fastbee.common.core.domain.entity.SysRoleIndex;
import com.fastbee.system.service.ISysRoleIndexService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.fastbee.common.core.controller.BaseController;
import com.fastbee.common.core.page.TableDataInfo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 角色首页配置
 */
@RestController
@RequestMapping("/sys/sysRoleIndex")
@Api(tags = "角色首页配置")
@Slf4j
public class SysRoleIndexController extends BaseController {

    @Autowired
    private ISysRoleIndexService sysRoleIndexService;

    @ApiOperation("查询角色首页配置列表")
    @GetMapping("/list")
    public TableDataInfo list(SysRoleIndex sysRoleIndex) {
        startPage();
        List<SysRoleIndex> list = sysRoleIndexService.selectRoleIndexList(sysRoleIndex);
        return getDataTable(list);
    }

    @ApiOperation("查询默认首页配置")
    @GetMapping("/queryDefIndex")
    public AjaxResult queryDefIndex() {
        SysRoleIndex defIndexCfg = sysRoleIndexService.queryDefaultIndex();
        return AjaxResult.success(defIndexCfg);
    }

    @ApiOperation("更新默认首页配置")
    @PreAuthorize("@ss.hasPermi('system:permission:setDefIndex')")
    @PutMapping("/updateDefIndex")
    public AjaxResult updateDefIndex(@RequestBody SysRoleIndex sysRoleIndex) {
        boolean success = sysRoleIndexService.updateDefaultIndex(sysRoleIndex);
        if (success) {
            return AjaxResult.success("设置成功");
        } else {
            return AjaxResult.error("设置失败");
        }
    }

    @ApiOperation("新增角色首页配置")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody SysRoleIndex sysRoleIndex) {
        return toAjax(sysRoleIndexService.insertRoleIndex(sysRoleIndex));
    }

    @ApiOperation("修改角色首页配置")
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody SysRoleIndex sysRoleIndex) {
        if (sysRoleIndex == null) {
            return AjaxResult.error("请求数据不能为空");
        }
        return toAjax(sysRoleIndexService.updateRoleIndex(sysRoleIndex));
    }

    @ApiOperation("删除角色首页配置")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(sysRoleIndexService.deleteRoleIndexByIds(ids));
    }

    @ApiOperation("根据ID查询角色首页配置")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(sysRoleIndexService.selectRoleIndexById(id));
    }
    /**
     * 通过code查询
     *
     * @param roleCode
     * @return
     */
    @ApiOperation("角色首页配置-通过code查询")
    @GetMapping(value = "/queryByCode")
    public AjaxResult queryByCode(@RequestParam(value = "roleCode", required = true) String roleCode, HttpServletRequest request) {
        SysRoleIndex sysRoleIndex = sysRoleIndexService.getOne(new LambdaQueryWrapper<SysRoleIndex>().eq(SysRoleIndex::getRoleCode, roleCode));
        return AjaxResult.success(sysRoleIndex);
    }

    /**
     * 清理默认首页缓存
     */
    @ApiOperation("清理默认首页缓存")
    @PostMapping("/cleanCache")
    public AjaxResult cleanCache() {
        sysRoleIndexService.cleanDefaultIndexCache();
        return AjaxResult.success("缓存清理成功");
    }
}