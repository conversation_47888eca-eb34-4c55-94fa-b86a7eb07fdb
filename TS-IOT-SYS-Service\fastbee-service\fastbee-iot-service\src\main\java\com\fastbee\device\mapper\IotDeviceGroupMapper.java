package com.fastbee.device.mapper;

import com.fastbee.device.domain.IotDeviceGroup;
import com.fastbee.device.domain.IotDeviceGroupInput;
import com.fastbee.device.domain.IotGroupInput;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 设备分组Mapper接口
 */
@Repository
public interface IotDeviceGroupMapper
{
    /**
     * 查询设备分组
     */
    public IotDeviceGroup selectdeviceGroupByGroupId(Long groupId);

    /**
     * 查询设备分组列表
     */
    public List<IotDeviceGroup> selectdeviceGroupList(IotDeviceGroup iotDeviceGroup);

    /**
     * 新增设备分组
     */
    public int insertdeviceGroup(IotDeviceGroup iotDeviceGroup);

    /**
     * 修改设备分组
     */
    public int updatedeviceGroup(IotDeviceGroup iotDeviceGroup);

    /**
     * 删除设备分组
     */
    public int deleteGroupByGroupId(Long groupId);

    public int deleteDeviceGroupByGroupId(Long groupIds);

    /**
     * 批量删除设备分组
     */
    public int deletedeviceGroupByGroupIds(Long[] groupIds);

    /**
     * 是否存在子节点
     */

    public int hasChildByGroupId(Long groupId);

    /**
     * 通过分组ID查询关联的设备ID数组
     */
    public List<IotGroupInput> selectDeviceIdsByGroupId(Long groupId);

    /**
     * 批量删除设备分组
     */
    public int deleteDeviceGroupByGroupIds(Long[] groupIds);


    /**
     * 批量删除分组
     */
    public int deleteGroupByGroupIds(Long[] groupIds);

    /**
     * 分组下批量增加设备分组
     */
    public int insertDeviceGroups(IotDeviceGroupInput input);

    public List<IotDeviceGroup> findByParentId(Long parentId);

    public IotDeviceGroup selectGroupByGroupName(String groupName);

    public List<IotDeviceGroup> selectGroupByDeviceId(Long deviceId);

    public Long selectGroupIdByGroupName(String groupName);
}
