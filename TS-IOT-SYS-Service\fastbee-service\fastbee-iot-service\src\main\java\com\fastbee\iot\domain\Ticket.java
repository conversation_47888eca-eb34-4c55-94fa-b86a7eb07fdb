package com.fastbee.iot.domain;

import com.fastbee.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(value = "Ticket", description = "工单实体 iot_ticket")
public class Ticket extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 工单ID */
    @ApiModelProperty("工单ID")
    private Long ticketId;

    /** 告警ID */
    @ApiModelProperty("告警ID")
    private Long alertLogId;

    /** 工单标题 */
    @ApiModelProperty("工单标题")
    private String title;

    /** 工单描述 */
    @ApiModelProperty("工单描述")
    private String description;

    /** 指派人员 */
    @ApiModelProperty("指派人员")
    private String assignedTo;

    /** 工单类型（1=报修单，2=巡检单） */
    @ApiModelProperty("工单类型（1=报修单，2=巡检单）")
    private Integer ticketType;

    /** 优先级别（1=低，2=普通，3=高，4=紧急，5=立刻） */
    @ApiModelProperty("优先级别（1=低，2=普通，3=高，4=紧急，5=立刻））")
    private Integer ticketLevel;

    /** 处理状态(1=新建,2=进行中,3=已解决,4=反馈,5=已关闭) */
    @ApiModelProperty("处理状态(1=新建,2=进行中,3=已解决,4=反馈,5=已关闭)")
    private Integer status;

    /** 截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dueDate;
    /** 设备编号 */
    @ApiModelProperty("设备编号")
    private String serialNumber;

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Long getTicketId() {
        return ticketId;
    }

    public void setTicketId(Long ticketId) {
        this.ticketId = ticketId;
    }

    public Long getAlertLogId() {
        return alertLogId;
    }

    public void setAlertLogId(Long alertLogId) {
        this.alertLogId = alertLogId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAssignedTo() {
        return assignedTo;
    }

    public void setAssignedTo(String assignedTo) {
        this.assignedTo = assignedTo;
    }

    public Integer getTicketType() {
        return ticketType;
    }

    public void setTicketType(Integer ticketType) {
        this.ticketType = ticketType;
    }

    public Integer getTicketLevel() {
        return ticketLevel;
    }

    public void setTicketLevel(Integer ticketLevel) {
        this.ticketLevel = ticketLevel;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }
}
