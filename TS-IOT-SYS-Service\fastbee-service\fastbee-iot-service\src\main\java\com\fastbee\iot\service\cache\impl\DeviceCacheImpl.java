package com.fastbee.iot.service.cache.impl;

import com.alibaba.fastjson2.JSONObject;
import com.fastbee.common.core.mq.DeviceStatusBo;
import com.fastbee.common.core.redis.RedisCache;
import com.fastbee.common.core.redis.RedisKeyBuilder;
import com.fastbee.common.enums.DeviceStatus;
import com.fastbee.common.exception.ServiceException;
import com.fastbee.common.utils.DateUtils;
import com.fastbee.common.utils.StringUtils;
import com.fastbee.device.domain.IotDevice;
import com.fastbee.device.service.IIotDeviceService;
import com.fastbee.iot.service.cache.IDeviceCache;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import javax.annotation.PreDestroy;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeviceCacheImpl implements IDeviceCache {

    @Value("${server.device.platform.expried:120}")
    private Integer expireTime;

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private IIotDeviceService deviceService;


    /**
     * 更新设备状态
     * 如果设备状态保持不变，更新redis设备最新在线时间
     * 如果设备状态更改，更新redis同时，更新MySQL数据库设备状态
     *
     * @param dto dto
     */
    @Override
    public IotDevice updateDeviceStatusCache(DeviceStatusBo dto) {

        IotDevice device = deviceService.selectDeviceBySerialNumber(dto.getSerialNumber());
        if (dto.getStatus() == DeviceStatus.ONLINE) {
            /*redis设备在线列表*/
            redisCache.zSetAdd(RedisKeyBuilder.buildDeviceOnlineListKey(), dto.getSerialNumber(), DateUtils.getTimestampSeconds());
            //更新mysql的设备状态为在线，延时500ms解决状态同步问题
            try {
                Thread.sleep(500); // 延迟一秒
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            device.setStatus(Long.valueOf(DeviceStatus.ONLINE.getType()));
        } else {
            /*在redis设备在线列表移除设备*/
            redisCache.zRem(RedisKeyBuilder.buildDeviceOnlineListKey(), dto.getSerialNumber());
            //更新一下mysql的设备状态为离线
            device.setStatus(Long.valueOf(DeviceStatus.OFFLINE.getType()));
        }
        device.setUpdateTime(DateUtils.getNowDate());
        deviceService.updateDeviceStatusAndLocation(device);
        return device;
    }

    /**
     * 更新设备状态
     * 如果设备状态保持不变，更新redis设备最新在线时间
     * 如果设备状态更改，更新redis同时，更新MySQL数据库设备状态
     *
     * @param dto dto
     */
    @Override
    public void updateDeviceStatusCache(DeviceStatusBo dto, IotDevice device) {

        Optional.ofNullable(device).orElseThrow(() -> new ServiceException("设备不存在" + "[{" + dto.getSerialNumber() + "}]"));
        if (dto.getStatus() == DeviceStatus.ONLINE) {
            /*redis设备在线列表*/
            redisCache.zSetAdd(RedisKeyBuilder.buildDeviceOnlineListKey(), dto.getSerialNumber(), DateUtils.getTimestampSeconds());
            device.setStatus(Long.valueOf(DeviceStatus.ONLINE.getType()));
        } else {
            /*在redis设备在线列表移除设备*/
            redisCache.zRem(RedisKeyBuilder.buildDeviceOnlineListKey(), dto.getSerialNumber());
            //更新一下mysql的设备状态为离线
            device.setStatus(Long.valueOf(DeviceStatus.OFFLINE.getType()));
        }
        device.setUpdateTime(DateUtils.getNowDate());
        deviceService.updateDeviceStatusAndLocation(device);

    }

    /**
     * 获取设备在线总数
     *
     * @return 设备在线总数
     */
    @Override
    public long deviceOnlineTotal() {
        return redisCache.zSize(RedisKeyBuilder.buildDeviceOnlineListKey());
    }


    /**
     * 批量更新redis缓存设备状态
     *
     * @param serialNumbers 设备列表
     * @param status        状态
     */
    @Override
    public void updateBatchDeviceStatusCache(List<String> serialNumbers, DeviceStatus status) {
        if (CollectionUtils.isEmpty(serialNumbers)) {
            return;
        }
        for (String serialNumber : serialNumbers) {
            DeviceStatusBo statusBo = new DeviceStatusBo();
            statusBo.setStatus(status);
            statusBo.setSerialNumber(serialNumber);
            this.updateDeviceStatusCache(statusBo);
        }
    }

    /**
     * 定时移除过期的设备
     */
    @SneakyThrows
    @Override
    public List<String> removeExpiredDevice() {
        List<String> serialNumberList = new ArrayList<String>();
        try {
            String cacheKey = RedisKeyBuilder.buildDeviceOnlineListKey();
            long n = DateUtils.getTimestampSeconds();
            long time = n - expireTime * 1000;
            Set<String> serialNumbers = redisCache.zRangeByScore(cacheKey, 0, time);
            redisCache.zRemBySocre(cacheKey, 0, time);
            if (CollectionUtils.isEmpty(serialNumbers)) {
                return serialNumberList;
            }
            return serialNumberList;
        } catch (Exception e) {
            log.warn("=>移除超时设备异常", e);
            return serialNumberList;
        }
    }

    @PreDestroy
    public void resetDeviceStatus(){
        System.out.println("执行--------------");
//        deviceService.reSetDeviceStatus();
    }

    /**
     * 定时移除过期的设备
     */
    @SneakyThrows
    @Override
    public List<String> removeExpiredDevice(Integer timeout) {
        List<String> serialNumberList = new ArrayList<String>();
        try {
            String cacheKey = RedisKeyBuilder.buildDeviceOnlineListKey();

            Set<String> serialNumbers = redisCache.zRange(cacheKey, 0, -1);
            if (CollectionUtils.isEmpty(serialNumbers)) {
                return serialNumberList;
            }

            // 当前时间定义时间格式，匹配你的 ts 格式："2025-05-15 15:30:58"
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime now = LocalDateTime.now();
            for (String sn : serialNumbers) {
                IotDevice device = deviceService.selectDeviceBySerialNumber(sn);
                if(device == null){
                    continue;
                }
                // 获取redis缓存的物模型值,如果120秒没更新数据则为下线
                String key = RedisKeyBuilder.buildTSLVCacheKey(device.getProductId(), sn);
                Map<String, String> map = redisCache.hashEntity(key);
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    String original = entry.getValue();
                    JSONObject originalJson = JSONObject.parseObject(original);
                    String ts = originalJson.getString("ts");
                    if (StringUtils.isNotBlank(ts)) {
                        try {
                            LocalDateTime tsTime = LocalDateTime.parse(ts, formatter);
                            long secondsDiff = Duration.between(tsTime, now).getSeconds();
                            if (secondsDiff > timeout) {
                                serialNumberList.add(sn);
                            }
                            // 只处理第一个有效的 ts，不管是否超时，都跳出循环
                            break;
                        } catch (Exception e) {
                            // ts 格式错误，继续下一个字段
                            System.err.println("ts 格式错误: " + ts);
                            continue;
                        }
                    }
                }
            }
            return serialNumberList;
        } catch (Exception e) {
            log.warn("=>移除超时设备异常", e);
            return serialNumberList;
        }
    }

}
