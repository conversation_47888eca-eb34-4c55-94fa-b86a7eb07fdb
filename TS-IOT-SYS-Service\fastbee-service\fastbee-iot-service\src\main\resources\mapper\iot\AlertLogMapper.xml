<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastbee.iot.mapper.AlertLogMapper">

    <resultMap type="com.fastbee.iot.domain.AlertLog" id="AlertLogResult">
        <result property="alertLogId"    column="alert_log_id"    />
        <result property="alertName"    column="alert_name"    />
        <result property="alertLevel"    column="alert_level"    />
        <result property="status"    column="status"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="productId"    column="product_id"    />
        <result property="detail"    column="detail"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="userId" column="user_id"/>
        <result property="sceneId" column="scene_id"/>
        <result property="scriptId" column="script_id"/>
        <result property="alertTime" column="alert_time"/>
    </resultMap>

    <sql id="selectAlertLogVo">
        select alert_log_id, alert_name, alert_level, status, product_id, detail, serial_number, create_by, create_time, remark, device_name, user_id,scene_id,script_id,alert_time
        from iot_alert_log
    </sql>

    <select id="selectAlertLogList" parameterType="com.fastbee.iot.domain.AlertLog" resultMap="AlertLogResult">
        select distinct al.alert_log_id, al.alert_name, al.alert_level, al.status, al.product_id, al.detail, al.serial_number, al.create_time, al.remark, al.device_name, al.user_id
        , al.scene_id, al.script_id, al.alert_time
        from iot_alert_log al
        <where>
            <if test="alertName != null  and alertName != ''"> and al.alert_name like concat('%', #{alertName}, '%')</if>
            <if test="alertLevel != null "> and al.alert_level = #{alertLevel}</if>
            <if test="status != null "> and al.status = #{status}</if>
            <if test="productId != null "> and al.product_id = #{productId}</if>
            <if test="serialNumber != null "> and al.serial_number = #{serialNumber}</if>
            <if test="userId != null "> and al.user_id = #{userId}</if>
        </where>
        order by al.create_time desc
    </select>

    <select id="selectAlertLog" parameterType="com.fastbee.iot.domain.AlertLog" resultMap="AlertLogResult">
        select distinct al.alert_log_id, al.status, al.detail, al.serial_number, al.create_time,al.scene_id,al.script_id
        from iot_alert_log al
        where al.status != 3 and al.serial_number = #{serialNumber} and al.scene_id = #{sceneId} and al.script_id = #{scriptId}
          and al.detail LIKE CONCAT('%"id": "', #{id}, '"%')
        order by al.create_time desc
    </select>

    <select id="selectAlertLogListCount" parameterType="com.fastbee.iot.domain.AlertLog" resultType="Long">
        select distinct count(l.alert_log_id)
        from iot_alert_log l
        left join iot_device d on l.serial_number=d.serial_number
        left join iot_device_user u on d.device_id=u.device_id
        <where>
            <if test="alertName != null  and alertName != ''"> and l.alert_name like concat('%', #{alertName}, '%')</if>
            <if test="alertLevel != null "> and l.alert_level = #{alertLevel}</if>
            <if test="status != null "> and l.status = #{status}</if>
            <if test="userId != null "> and u.user_id = #{userId}</if>
            <if test="productId != null "> and l.product_id = #{productId}</if>
        </where>
    </select>

    <select id="selectAlertLogByAlertLogId" parameterType="Long" resultMap="AlertLogResult">
        <include refid="selectAlertLogVo"/>
        where alert_log_id = #{alertLogId}
    </select>

    <insert id="insertAlertLog" parameterType="com.fastbee.iot.domain.AlertLog" useGeneratedKeys="true" keyProperty="alertLogId">
        insert into iot_alert_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="alertName != null and alertName != ''">alert_name,</if>
            <if test="alertLevel != null">alert_level,</if>
            <if test="status != null">status,</if>
            <if test="productId != null">product_id,</if>
            <if test="detail != null and detail != ''">detail,</if>
            <if test="serialNumber != null and serialNumber != ''">serial_number,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="userId != null">user_id,</if>
            <if test="deviceName != null and deviceName != ''">device_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="alertName != null and alertName != ''">#{alertName},</if>
            <if test="alertLevel != null">#{alertLevel},</if>
            <if test="status != null">#{status},</if>
            <if test="productId != null">#{productId},</if>
            <if test="detail != null and detail != ''">#{detail},</if>
            <if test="serialNumber != null and serialNumber != ''">#{serialNumber},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deviceName != null and deviceName != ''">#{deviceName},</if>
         </trim>
    </insert>

    <insert id="insertAlertLogBatch" parameterType="com.fastbee.iot.domain.AlertLog" useGeneratedKeys="true" keyProperty="alertLogId">
        insert into iot_alert_log (alert_name,alert_level,status,product_id,detail,serial_number,create_time,device_name,remark,user_id,scene_id,script_id,alert_time)
        values
        <foreach collection ="list" item="alertLog" separator =",">
            (#{alertLog.alertName},
            #{alertLog.alertLevel},
            #{alertLog.status},
            #{alertLog.productId},
            #{alertLog.detail},
            #{alertLog.serialNumber},
            #{alertLog.createTime},
            #{alertLog.deviceName},
            #{alertLog.remark},
            #{alertLog.userId},
            #{alertLog.sceneId},
            #{alertLog.scriptId},
            #{alertLog.alertTime})
        </foreach >
    </insert>
    <update id="updateAlertLogBatch" parameterType="com.fastbee.iot.domain.AlertLog">
        update iot_alert_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="alertName != null and alertName != ''">alert_name = #{alertName},</if>
            <if test="detail != null and detail != ''">detail = #{detail},</if>
            <if test="alertTime != null">alert_time = #{alertTime},</if>
        </trim>
        where alert_log_id = #{alertLogId}
    </update>
    <update id="updateAlertLog" parameterType="com.fastbee.iot.domain.AlertLog">
        update iot_alert_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="alertName != null and alertName != ''">alert_name = #{alertName},</if>
            <if test="alertLevel != null">alert_level = #{alertLevel},</if>
            <if test="status != null">status = #{status},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="detail != null and detail != ''">detail = #{detail},</if>
            <if test="serialNumber != null and serialNumber != ''">serial_number = #{serialNumber},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where alert_log_id = #{alertLogId}
    </update>

    <delete id="deleteAlertLogByAlertLogId" parameterType="Long">
        delete from iot_alert_log where alert_log_id = #{alertLogId}
    </delete>

    <delete id="deleteAlertLogBySerialNumber" parameterType="String">
        delete from iot_alert_log where serial_number = #{serialNumber}
    </delete>

    <delete id="deleteAlertLogByAlertLogIds" parameterType="String">
        delete from iot_alert_log where alert_log_id in
        <foreach item="alertLogId" collection="array" open="(" separator="," close=")">
            #{alertLogId}
        </foreach>
    </delete>

</mapper>
