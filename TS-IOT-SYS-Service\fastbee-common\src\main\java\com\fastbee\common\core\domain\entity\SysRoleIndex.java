package com.fastbee.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fastbee.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 角色首页配置
 */
@Data
@TableName("sys_role_index")
@ApiModel(value = "角色首页配置")
public class SysRoleIndex extends BaseEntity {
	@TableId(type = IdType.ASSIGN_ID)
	@ApiModelProperty(value = "主键")
	private String id;

	@ApiModelProperty(value = "角色编码")
	private String roleCode;

	@ApiModelProperty(value = "路由地址")
	private String url;

	@ApiModelProperty(value = "组件")
	private String component;

	@ApiModelProperty(value = "是否路由菜单")
	private Boolean isRoute;

	@ApiModelProperty(value = "优先级")
	private Integer priority;

	@ApiModelProperty(value = "状态")
	private String status;

	// 查询条件字段（不映射到数据库）
	@TableField(exist = false)
	@ApiModelProperty(value = "角色编码列表", hidden = true)
	private List<String> roleCodes;

	// 排除 BaseEntity 中不存在于数据库表的字段
	@TableField(exist = false)
	private String searchValue;

	@TableField(exist = false)
	private String remark;

	@TableField(exist = false)
	private java.util.Map<String, Object> params;

	// 构造函数
	public SysRoleIndex() {}

	public SysRoleIndex(String componentUrl) {
		this.component = componentUrl;
	}
}