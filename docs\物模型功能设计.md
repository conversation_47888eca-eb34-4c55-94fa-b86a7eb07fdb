## 物模型功能设计

{{>toc}}



### 1. 物模型相关功能ER图


{{mermaid(ER图,showsource=true)
erDiagram
IOT_CATEGORY|o--o{IOT_PRODUCT : contains
IOT_PRODUCT |o--o{IOT_DEVICE : contains
IOT_PRODUCT }|--|{ IOT_Things_Model : has
IOT_DEVICE ||--|{ IOT_Things_Model_Value : has
IOT_DEVICE ||--|{ IOT_DEVICE_LOG : trace
IOT_DEVICE }|--o| IOT_GROUP : in
IOT_DEVICE }|--|| IOT_USER : "Belong to"
}}


### 2. 实体关系说明

其中，`IOT_CATEGORY` 指的是产品类型、`IOT_PRODUCT` 指的是产品、`IOT_DEVICE` 指的是设备、`IOT_Things_Model` 指的是物模型属性定义、`IOT_Things_Model_Value` 指的是设备物模型属性的值、`IOT_DEVICE_LOG` 指的是设备物模型属性中遥测属性的实时数值及记录、`IOT_GROUP`指设备分组、`IOT_USER`指的是设备业主用户。

####  2.1 IOT_CATEGORY (产品类型)：

1. 一个产品类型 (`IOT_CATEGORY`) 可以包含多个产品 (`IOT_PRODUCT`)，但每个产品 (`IOT_PRODUCT`)只能属于一个产品类型 (`IOT_CATEGORY`) 。
2. 产品类型 (`IOT_CATEGORY`) 应该有树形关系。
3. 加入模板产品，用于管理基础的物模型参数定义，便于真实产品集成。
   
####  2.2 IOT_PRODUCT (产品)：

1. 一个产品 (`IOT_PRODUCT`) 可以包含多个设备 (`IOT_DEVICE`)，但每个设备只能属于一个产品。
2. 一个产品 (`IOT_PRODUCT`) 对应一套物模型属性模板 (`IOT_Things_Model_Templates`)，但物模型属性模版可以分发给多个设备。
3. 一个产品 (`IOT_PRODUCT`) 按照数据连接方式可以分为直连设备、网关设备、网关子设备、监控设备和固定设备。
	- 直连设备：设备本身具备联网能力，能够不通过网关直接连接到物联网平台。
	- 网关设备：能够直接连接物联网平台的设备，且具有子设备管理功能，能够代理子设备连接物联网平台。
	- 网关子设备：不能直接连接物联网平台的设备，需要通过网关代理连接到物联网平台。
	- 监控设备：支持国标GB28181的摄像头、NVR等视频监控联网设备。
	- 固定设备：纯设备资产信息化存档，无网络连接、无实时数据接入，便于信息归集，比如投影仪，录入相关信息后可将更换灯泡、日常保养等工单记录与设备关联。
	- 虚拟设备：对一些特殊情况进行抽象便于管理，如常用属性的物模型模板可以创建一个虚拟设备将基本的物模型属性进行绑定

#### 2.3 IOT_Things_Model (物模型参数定义)：

物模型是产品数字化的描述，定义了产品的功能，物模型将不同品牌不同品类的产品功能抽象归纳，形成“标准物模型”，便于各方用统一的语言描述、控制、理解产品功能。 物模型由若干条“参数定义”组成，物模型参数定义(`IOT_Things_Model`)按描述的功能类型不同，又分为属性定义、功能定义、事件定义和关系定义：

- 属性：用于描述设备具体信息和状态。可以设计属性标签将属性分为如下几类，并保留一定的设计弹性。
	1. 固有属性：例如生产厂家、出厂编号等
	2. 运维属性：例如安装位置、检修周期、现场照片等
	3. 遥测属性：例如实时压力值、瞬时流量、累计流量等
	4. 通讯属性：例如IP、端口、从站地址、寄存器地址、URL路径、MQTT Topic等
	5. 虚拟属性：例如数字孪生平台中模型、数字孪生场景中坐标、朝向、角度等
- 服务：设备可被外部调用的能力或方法，可设置输入参数和输出参数。相比于属性，服务可通过一条指令实现更复杂的业务逻辑，如执行某项特定的任务。
- 事件：设备运行时，主动上报给云端的信息，或者通过设备上报的数据分析出来的需要关注的信息。一般包含需要被外部感知和处理的信息、告警和故障。事件中可包含多个输出参数。例如，某项任务完成后的通知信息；设备发生故障时的温度、时间信息；设备告警时的运行状态等。事件可以被订阅和推送。
- 关系：描述设备之间的关系，例如行车记录仪、OBD检测仪安装在汽车上，行车记录仪、OBD检测仪和汽车分别是独立的设备，但是选择汽车的时候应该把其附属设备行车记录仪和OBD检测仪相关的数据一同显示。关系应该至少包含两类：
	1. 物理关系：比如附属关系、包含关系、备用关系等等
	2. 逻辑关系：比如网关和网关子设备的关联，比如网络拓扑中的链式、树形、环形关系等

#### 2.4 IOT_DEVICE (设备)

1. 每个设备 (`IOT_DEVICE`) 也包含多个物模型参数定义 (`IOT_Things_Model`)，
2. 每个设备 (`IOT_DEVICE`) 的物模型参数定义 (`IOT_Things_Model`)需要实例化，通过物模型属性的值 (`IOT_Things_Model_Value`)与遥测属性的日志记录 (`IOT_DEVICE_LOG`)进行数据绑定。

#### 2.5 IOT_Things_Model_Value (物模型参数的值)

1. 每个设备 (`IOT_DEVICE`) 可以有多个物模型参数的值 (`IOT_Things_Model_Value`)。
2. 每一个物模型参数的值 (`IOT_Things_Model_Value`)与对应物模型参数定义(`IOT_Things_Model`)中的数据格式要求要匹配。
3. 物模型参数的值 (`IOT_Things_Model_Value`)不包含遥测属性所对应的值。


#### 2.6 IOT_DEVICE_LOG (遥测属性实时记录)

 1. 每个设备 (`IOT_DEVICE`) 可以有多条遥测属性，每个遥测属性都会不停的产生遥测属性实时记录 (`IOT_DEVICE_LOG`)。
 2. 遥测属性实时记录 (`IOT_DEVICE_LOG`)是连续的时序数据，数据量大的时候迁移到时序数据库中管理。


#### 2.7 IOT_GROUP (设备分组)

 1. 一个设备 (`IOT_DEVICE`) 可以属于多个设备分组 (`IOT_GROUP`)，一个设备分组可以包含多个设备。

   
#### 2.8 IOT_USER (设备业主用户)

1. 一个设备 (`IOT_DEVICE`) 只能属于一个用户 (`IOT_USER`)，但一个用户可以拥有多个设备。

---

### 3. 有争议问题

- ~~**物模型属性定义的重复关系**~~

> **问题**：当前设计中，`IOT_PRODUCT` 和 `IOT_DEVICE` 都与 `IOT_Things_Model` 有直接的关系。这可能导致数据冗余和不一致。 考虑只在 `IOT_PRODUCT` 和 `IOT_Things_Model` 之间建立关系，并通过产品来间接关联设备的物模型属性。 `IOT_DEVICE`会缺少一些灵活性，但是在查询和管理上会方便很多。
> **结论**：物模型属性中加入一个JSON格式的备注属性，对于每一个设备的特殊需求进行单独调整。

---

### 4. 加入关键字段形成ER图

> 按照上面的实体及关系设计形成数据库定义 模拟系统需要使用的操作 

根据上述描述，我将为每个实体添加一些基本字段，以便更详细地定义ER图。以下是更新后的Mermaid ER Diagram代码：

{{mermaid(ER图,showsource=true)
erDiagram
	IOT_CATEGORY {
		bigint category_id PK "产品分类ID"
		varchar category_name "产品分类名称"
		bigint parent_id "父级ID"
		int order_num "显示顺序"
		char del_flag "删除标志（0代表存在 2代表删除）"
		varchar remark "备注"
	}
    
	IOT_PRODUCT {
		bigint product_id PK "产品ID"
		varchar product_name "产品名称"
		bigint category_id "产品分类ID"
		tinyint status "状态（1-未发布，2-已发布）"
		tinyint device_type "产品类型（1=直连设备，2=网关设备，3=网关子设备，4=监控设备，5=固定设备，6=虚拟设备）"
		varchar img_url "图片地址"
		char del_flag "删除标志（0代表存在 2代表删除）"
		varchar remark "备注"
    }

	IOT_Product_ThingsModels {
		bigint product_id  FK "产品ID"
		bigint model_id FK "物模型ID"
	}

	IOT_Things_Model {
        bigint model_id PK "物模型参数ID"
		varchar model_name "物模型参数名称"
		varchar identifier "标识符，产品下唯一（物模型参数英文名）"
		tinyint type "物模型参数类型（1=属性，2=服务，3=事件，4=关系）"
		tinyint sub_type "参数子类型（1=固有属性，2=运维属性，3=遥测属性，4=通讯属性，5=虚拟属性）"
        int attribute "参数特性，如是否只读、是否图表、是否唯一、是否必填等，使用二进制位运算进行设定与查看"
		varchar datatype "数据类型（integer、decimal、string、bool、array、enum、json、file）"
		json specs "数据定义，各类型数据不同数值的含义进行解释"
		int model_order "排序，值越大，排序越靠前"
		char del_flag "删除标志（0代表存在 2代表删除）"
		varchar remark "备注"
    }

	IOT_DEVICE {
		bigint device_id PK "设备ID"
		varchar device_name "设备名称"
		bigint product_id "产品ID"
		varchar serial_number "设备编号，唯一，将来作为MQTT主题查询依据"
		varchar img_url "图片地址"	
		tinyint status "设备状态（1-未激活，2-禁用，3-在线，4-离线）"
		char del_flag "删除标志（0代表存在 2代表删除）"
		varchar remark "备注"
	}
    
    IOT_Things_Model_Value {
        int id PK "索引ID"
        int device_id FK "设备ID"
        int model_id "物模型参数ID"
        varchar value "参数在设备中的具体值"
        varchar identifier FK "标识符，产品下唯一（物模型参数英文名）"
    }

    IOT_DEVICE_LOG {
        bigint log_id PK "设备遥测属性记录ID"
        varchar identifier FK "标识符，产品下唯一（物模型参数英文名）"     
        varchar serial_number FK "设备编号，唯一，将来作为MQTT主题查询依据" 
        tinyint log_type "类型（1=属性上报，2=调用功能，3=事件上报，4=设备升级，5=设备上线，6=设备离线）"
        varchar log_value "日志值"
		datetime log_time "日志记录时间"
        varchar remark "备注"
    }
    
	IOT_GROUP {
		bigint group_id PK "设备分组ID"
		varchar group_name "设备分组名称"
		tinyint group_order "设备分组排序"
		bigint parent_id "父级ID"
		char del_flag "删除标志（0代表存在 2代表删除）"
		varchar remark "备注"
	}
    
    IOT_USER {
        bigint user_id PK "用户ID"
        varchar user_name "用户昵称"
        char del_flag "删除标志（0代表存在 2代表删除）"
        varchar remark "备注"
    }

	IOT_DEVICE_USER {
	    bigint id PK "关联ID"
        bigint user_id FK "用户ID"
		bigint device_id FK "设备ID"
		tinyint is_owner "是否为设备所有者（0=否，1=是）"
        varchar perms "用户物模型权限，多个以英文逗号分隔"
        varchar remark "备注"
	}

	IOT_DEVICE_GROUP {
		bigint device_id FK "设备ID"
		bigint group_id PK "设备分组ID"
	}

    IOT_CATEGORY ||--o{ IOT_PRODUCT : contains
    IOT_PRODUCT }o--o{  IOT_Product_ThingsModels : uses
    IOT_Product_ThingsModels }o--o{  IOT_Things_Model : defines
    IOT_PRODUCT ||--o{ IOT_DEVICE : contains
    IOT_DEVICE ||--o{ IOT_Things_Model_Value : has_value_for
    IOT_DEVICE ||--o{ IOT_DEVICE_LOG : logs_telemetry_for
    IOT_GROUP }o--o{ IOT_DEVICE_GROUP  : contains
    IOT_DEVICE_GROUP }o--o{ IOT_DEVICE  : contains
    IOT_USER }o--o{ IOT_DEVICE_USER  : owns
    IOT_DEVICE_USER }o--o{ IOT_DEVICE  : owns

}}

此ER图定义了每个实体的基本字段，并根据描述建立了正确的关系。请注意，`parent_id`在`IOT_CATEGORY`表中用于创建类别之间的层次结构（如子类目）。此外，`parent_template_id`在`IOT_Things_Model_Templates`表中用于支持模板继承的概念。

### 5. 使用SQL语句进行功能推演

#### 5.1 根据上述ER图创建数据库和测试表

```sql
CREATE TABLE IOT_CATEGORY (
    category_id BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT '产品分类ID',
    category_name VARCHAR(255) NOT NULL COMMENT '产品分类名称',
    parent_id BIGINT COMMENT '父级ID',
    order_num INT COMMENT '显示顺序',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    remark VARCHAR(255) COMMENT '备注',
    create_by varchar(64) COMMENT '创建者',
    create_time datetime(0) COMMENT '创建时间',
    update_by varchar(64) COMMENT '更新者',
    update_time datetime(0) COMMENT '更新时间'
);

CREATE TABLE IOT_PRODUCT (
    product_id BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT '产品ID',
    product_name VARCHAR(255) NOT NULL COMMENT '产品名称',
    category_id BIGINT NOT NULL COMMENT '产品分类ID',
    status TINYINT COMMENT '状态（1-未发布，2-已发布）',
    device_type TINYINT COMMENT '产品类型（1=直连设备，2=网关设备，3=网关子设备，4=监控设备，5=固定设备，6=虚拟设备）',
    img_url VARCHAR(255) COMMENT '图片地址',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    remark VARCHAR(255) COMMENT '备注',
    create_by varchar(64) COMMENT '创建者',
    create_time datetime(0) COMMENT '创建时间',
    update_by varchar(64) COMMENT '更新者',
    update_time datetime(0) COMMENT '更新时间'
);

CREATE TABLE IOT_Things_Model (
    model_id BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT '物模型参数ID',
    model_name VARCHAR(255) NOT NULL COMMENT '物模型参数名称',
    identifier VARCHAR(255) NOT NULL COMMENT '标识符，产品下唯一（物模型参数英文名）',
    type TINYINT NOT NULL COMMENT '物模型参数类型（1=属性，2=服务，3=事件，4=关系）',
    sub_type TINYINT NOT NULL COMMENT '参数子类型（1=固有属性，2=运维属性，3=遥测属性，4=通讯属性，5=虚拟属性）',
    attribute INT NOT NULL DEFAULT 0 COMMENT '参数特性，如是否只读、是否图表、是否唯一、是否必填等，使用二进制位运算进行设定与查看',
    datatype VARCHAR(255) NOT NULL COMMENT '数据类型（integer、decimal、string、bool、array、enum、json、file）',
    specs json COMMENT '数据定义，各类型数据不同数值的含义进行解释',
    model_order INT COMMENT '排序，值越大，排序越靠前',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    remark VARCHAR(255) COMMENT '备注',
    create_by varchar(64) COMMENT '创建者',
    create_time datetime(0) COMMENT '创建时间',
    update_by varchar(64) COMMENT '更新者',
    update_time datetime(0) COMMENT '更新时间'
);

CREATE TABLE IOT_Product_ThingsModels (
    product_id BIGINT NOT NULL COMMENT '产品ID',
    model_id BIGINT NOT NULL COMMENT '物模型ID',
    PRIMARY KEY (product_id, model_id) USING BTREE
);

CREATE TABLE IOT_DEVICE (
    device_id BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT '设备ID',
    device_name VARCHAR(255) NOT NULL COMMENT '设备名称',
    product_id BIGINT NOT NULL COMMENT '产品ID',
    product_name VARCHAR(255) NOT NULL COMMENT '产品名称',
    serial_number VARCHAR(255) NOT NULL COMMENT '设备编号，唯一，将来作为MQTT主题查询依据',
    img_url VARCHAR(255) COMMENT '图片地址',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '设备状态（1-未激活，2-禁用，3-在线，4-离线）',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    remark VARCHAR(255) COMMENT '备注',
    active_time datetime(0) COMMENT '激活时间',
    create_by varchar(64) COMMENT '创建者',
    create_time datetime(0) COMMENT '创建时间',
    update_by varchar(64) COMMENT '更新者',
    update_time datetime(0) COMMENT '更新时间'
);

CREATE TABLE IOT_Things_Model_Value (
    id BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT '索引ID',
    device_id BIGINT NOT NULL COMMENT '设备ID',
    model_id BIGINT NOT NULL COMMENT '物模型参数ID',
    value VARCHAR(255) COMMENT '参数在设备中的具体值',
    identifier VARCHAR(255) NOT NULL COMMENT '标识符，产品下唯一（物模型参数英文名）'
);

CREATE TABLE IOT_DEVICE_LOG (
    log_id BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT '设备遥测属性记录ID',
    device_id BIGINT COMMENT '设备ID',
    identifier VARCHAR(255) NOT NULL COMMENT '标识符，产品下唯一（物模型参数英文名）',
    model_name VARCHAR(255) NOT NULL COMMENT '物模型参数名称',
    log_type TINYINT NOT NULL COMMENT '类型（1=属性上报，2=调用功能，3=事件上报，4=设备升级，5=设备上线，6=设备离线）',
    log_value VARCHAR(255) NOT NULL COMMENT '日志值',
    serial_number VARCHAR(255) NOT NULL COMMENT '设备编号，唯一，将来作为MQTT主题查询依据',
    is_monitor tinyint(1) DEFAULT 0 COMMENT '是否监测数据（1=是，0=否）',
    create_by varchar(64) COMMENT '创建者',
    create_time datetime(0) COMMENT '创建时间',
    remark VARCHAR(255) COMMENT '备注'
);

CREATE TABLE IOT_GROUP (
    group_id BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT '设备分组ID',
    group_name VARCHAR(255) NOT NULL COMMENT '设备分组名称',
    group_order TINYINT NOT NULL COMMENT '设备分组排序',
    parent_id BIGINT NOT NULL DEFAULT 0 COMMENT '父级ID',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    remark VARCHAR(255) COMMENT '备注',
    create_by varchar(64) COMMENT '创建者',
    create_time datetime(0) COMMENT '创建时间',
    update_by varchar(64) COMMENT '更新者',
    update_time datetime(0) COMMENT '更新时间'
);

CREATE TABLE IOT_USER (
    user_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    user_name VARCHAR(255) COMMENT '用户昵称',
    del_flag CHAR(1) COMMENT '删除标志（0代表存在 2代表删除）',
    remark VARCHAR(255) COMMENT '备注'
);

CREATE TABLE IOT_DEVICE_USER (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    user_id BIGINT COMMENT '用户ID',
    device_id BIGINT COMMENT '设备ID',
    is_owner TINYINT COMMENT '是否为设备所有者（0=否，1=是）',
    perms VARCHAR(255) COMMENT '用户物模型权限，多个以英文逗号分隔',
    remark VARCHAR(255) COMMENT '备注'
);

CREATE TABLE IOT_DEVICE_GROUP (
    device_id BIGINT NOT NULL COMMENT '设备ID',
    group_id BIGINT NOT NULL COMMENT '设备分组ID',
    PRIMARY KEY (device_id, group_id) USING BTREE
);


```

---

#### 5.2 功能验证

##### 5.2.1 物模型参数管理

> 一、插入物模型参数

```sql
-- 插入物模型固有属性（例如生产厂家）
INSERT INTO iot_things_model (model_name,identifier,`type`,sub_type,datatype,specs,model_order,del_flag,remark) VALUES
	 ('生产厂家','manufacturer',1,1,'string',NULL,1,'0','设备的生产厂家'),
	 ('现场照片','site_photo',1,2,'string',NULL,2,'0','设备安装位置的照片链接');
	 
-- 插入物模型通讯属性（例如设备IP、MQTT连接信息）
INSERT INTO iot_things_model (model_name,identifier,`type`,sub_type,datatype,specs,model_order,del_flag,remark) VALUES
	 ('设备IP','device_ip',1,4,'string',NULL,3,'0','设备的IP地址'),
	 ('MQTT连接信息','mqtt_connection',1,4,'json','{"host": "IP", "port": "端口号", "topic": "MQTT主题", "password": "MQTT密码", "username": "MQTT用户名"}',4,'0','设备的MQTT连接信息');
	 
-- 插入物模型遥测属性（例如压力值、当前流量）
INSERT INTO iot_things_model (model_name,identifier,`type`,sub_type,datatype,specs,model_order,del_flag,remark) VALUES
	 ('压力值','pressure',1,3,'decimal','{"max": 120, "min": -20, "step": 0.1, "type": "decimal", "unit": "Pa"}',5,'0','设备的压力值'),
	 ('当前流量','current_flow',1,3,'decimal','{"unit": "m³/h"}',6,'0','设备的当前流量');
	 
-- 插入物模型虚拟属性（例如数字孪生模型路径）
INSERT INTO iot_things_model (model_name,identifier,`type`,sub_type,datatype,specs,model_order,del_flag,remark) VALUES
	 ('数字孪生模型路径','digital_twin_path',1,5,'string',NULL,7,'0','数字孪生平台中的设备模型路径');
	 
-- 插入物模型服务（例如启动电机）
INSERT INTO iot_things_model (model_name,identifier,`type`,sub_type,datatype,specs,model_order,del_flag,remark) VALUES
	 ('启动电机','start_motor',2,1,'string','{"parameters": {"motor_id": "电机ID"}}',8,'0','用于启动电机的驱动函数');
	 
-- 插入物模型事件（例如气体泄漏事件）
INSERT INTO iot_things_model (model_name,identifier,`type`,sub_type,datatype,specs,model_order,del_flag,remark) VALUES
	 ('气体泄漏','gas_leak',3,1,'boolean','{"description": "设备检测到气体泄漏"}',9,'0','设备发生气体泄漏时的事件回调函数');

-- 插入物模型关系（例如父节点设备ID）
INSERT INTO iot_things_model (model_name,identifier,`type`,sub_type,datatype,specs,model_order,del_flag,remark) VALUES
	 ('父节点设备ID','parent_device_id',4,1,'bigint',NULL,10,'0','该设备所属的父节点设备ID');	

```

> 二、查询、修改物模型参数

```sql
-- 查询所有物模型参数信息
SELECT model_id, model_name, identifier, type, sub_type, datatype, specs, model_order, del_flag, remark
FROM IOT_Things_Model;

--根据 `type`和`sub_type` 查询特定类别的物模型参数（例如遥测属性）：
-- 查询遥测属性
SELECT model_id,model_name,identifier,type,sub_type,datatype,specs,model_order,del_flag,remark 
FROM IOT_Things_Model
WHERE type = 1 AND sub_type = 3;

-- 更新物模型的备注信息
UPDATE IOT_Things_Model
SET remark = '更新后的备注信息'
WHERE model_id = 1;

```

>  三、删除物模型参数

删除某个物模型参数，删除前需要在产品与物模型参数关联表(`IOT_Product_ThingsModels`)和设备物模型赋值关联表`IOT_Things_Model_Value`删除相关的关联。

```sql
-- 1. 删除产品与物模型参数的关联
DELETE FROM IOT_Product_ThingsModels
WHERE model_id = 1;  -- 假设要删除的物模型参数ID为1

-- 2. 删除设备与物模型参数值的关联
DELETE FROM IOT_Things_Model_Value
WHERE model_id = 1;  -- 假设要删除的物模型参数ID为1

-- 3. 删除物模型参数（逻辑删除，设置 del_flag 为 2）
UPDATE IOT_Things_Model
SET del_flag = '2'
WHERE model_id = 1;  -- 假设要删除的物模型参数ID为1

```

可以考虑使用事务来完成，或者在前端流程中进行一步一步的确认。

```sql

START TRANSACTION;

DELETE FROM IOT_Product_ThingsModels WHERE model_id = 1;
DELETE FROM IOT_Things_Model_Value WHERE model_id = 1;
UPDATE IOT_Things_Model SET del_flag = '2' WHERE model_id = 1;

COMMIT;

```

---

##### 5.2.2 产品管理



> 一、产品分类信息的增删改查

```sql

-- 插入智能家居分类
INSERT INTO IOT_CATEGORY (category_name, parent_id, order_num, del_flag, remark)
VALUES ('智能家居', NULL, 1, '0', '智能家居产品分类');

-- 插入智能音箱分类，作为智能家居的子分类
INSERT INTO IOT_CATEGORY (category_name, parent_id, order_num, del_flag, remark)
VALUES ('智能音箱', 1, 2, '0', '智能音箱产品分类');

-- 查询所有产品分类
SELECT category_id, category_name, parent_id, order_num, del_flag, remark
FROM IOT_CATEGORY;

-- 查询某个分类的子分类
SELECT category_id, category_name, parent_id, order_num, del_flag, remark
FROM IOT_CATEGORY
WHERE parent_id = 1;  -- 假设父分类ID为1

-- 更新产品分类名称
UPDATE IOT_CATEGORY
SET category_name = '智能家电'
WHERE category_id = 1;

-- 删除产品分类（逻辑删除，设置 del_flag 为 2）
UPDATE IOT_CATEGORY
SET del_flag = '2'
WHERE category_id = 1;

```


> 二、产品信息的添加与查询


```sql

-- 插入产品
INSERT INTO IOT_PRODUCT (product_name, category_id, status, device_type, img_url, del_flag, remark)
VALUES ('智能灯泡', 1, 2, 1, 'https://example.com/bulb.jpg', '0', '智能灯泡产品');

-- 查询所有产品
SELECT product_id, product_name, category_id, status, device_type, img_url, del_flag, remark
FROM IOT_PRODUCT;

-- 更新产品描述
UPDATE IOT_PRODUCT
SET remark = '一款高性能智能灯泡'
WHERE product_id = 1;

-- 查询某个分类下的产品
SELECT product_id, product_name, category_id, status, device_type, img_url, del_flag, remark
FROM IOT_PRODUCT
WHERE category_id = 1;  -- 假设分类ID为1

```


> 三、产品信息的修改与查询

删除产品或修改产品的发布状态，要同时考虑与其关联的设备`IOT_DEVICE`情况。

```sql

-- 更新所有有产品类型为product_id为1的设备状态为禁用
UPDATE IOT_DEVICE
SET status = 2
WHERE product_id = 1;

-- 删除所有有产品类型为product_id为1的设备
DELETE FROM IOT_DEVICE
WHERE product_id = 1;

-- 更新产品状态
UPDATE IOT_PRODUCT
SET status = 1  -- 1=未发布
WHERE product_id = 1;

-- 删除产品（逻辑删除，设置 del_flag 为 2）
UPDATE IOT_PRODUCT
SET del_flag = '2'
WHERE product_id = 1;

```


> 四、产品与物模型参数的关联与绑定

产品与物模型参数的绑定是通过 `IOT_Product_ThingsModels` 表来实现的。每一个产品都有一套完整物模型参数模板：

```sql
-- 插入产品与物模型参数定义的关联，多对多
-- 对产品id为1的产品添加物模型参数 1-8
INSERT INTO IOT_Product_ThingsModels (product_id, model_id)
VALUES (1, 2),(1, 3),(1, 4),(1, 5),(1, 6),(1, 7),(1, 8);

-- 对物模型参数 1（生产厂家）赋予到1-8款产品中
INSERT INTO IOT_Product_ThingsModels (product_id, model_id)
VALUES (1, 1),(2, 1),(3, 1),(4, 1),(5, 1);

-- 查询一款产品（Product_id=1）中的所有物模型参数名称
SELECT tm.model_name
FROM IOT_Product_ThingsModels ptm
JOIN IOT_Things_Model tm ON ptm.model_id = tm.model_id
WHERE ptm.product_id = 1;

```

---

##### 5.2.3 设备管理

> 一、设备的增删改查

```sql
-- 插入设备
INSERT INTO IOT_DEVICE (device_name, product_id, status, location, img_url, del_flag, remark)
VALUES ('智能灯泡A', 1, 2, '客厅', 'https://example.com/bulb.jpg', '0', '智能灯泡产品');

-- 查询设备基础信息
SELECT device_id, device_name, product_id, status, location, img_url, del_flag, remark
FROM IOT_DEVICE;

-- 更新设备状态
UPDATE IOT_DEVICE
SET status = 1
WHERE device_id = 1;

-- 删除设备
DELETE FROM IOT_DEVICE
WHERE device_id = 1;

```

---

> 二、设备与物模型参数值绑定管理

设备与物模型参数值的绑定是通过 `IOT_Things_Model_Value` 表来实现的。以下是一些操作示例：

```sql
-- 为设备绑定物模型参数值 数据类型统一为text 使用前要进行类型转换
INSERT INTO IOT_Things_Model_Value (device_id, things_model_id, value)
VALUES (1, 1, '生产厂家A');
```

> 三、查询设备的所有物模型参数及其值：

```sql
-- 查询设备的所有物模型参数及值
SELECT d.device_name, t.model_name, v.value
FROM IOT_DEVICE d
JOIN IOT_Things_Model_Value v ON d.device_id = v.device_id
JOIN IOT_Things_Model t ON v.model_id = t.model_id
WHERE d.device_id = 1;


-- 修改设备物模型参数的值，如修改设备厂家
UPDATE IOT_Things_Model_Value
SET value = '生产厂家B'
WHERE device_id = 1 AND things_model_id = 1;
```

---

> 四、设备遥测属性日志管理

设备日志表 `IOT_DEVICE_LOG` 用于记录设备的遥测数据。以下是一些常见的操作：

```sql
-- 插入设备日志
INSERT INTO IOT_DEVICE_LOG (iot_device_id, iot_things_model_id, log_type, log_value, log_time, remark)
VALUES (1, 1, 1, '压力值记录', NOW(), '设备压力值的日志');

-- 查询设备日志
SELECT log_id, log_type, log_value, log_time
FROM IOT_DEVICE_LOG
WHERE iot_device_id = 1
ORDER BY log_time DESC;

-- 更新设备日志 （原则上应该是不能更新的 保留原始记录，必要时为了数据分析的需要可以考虑）
UPDATE IOT_DEVICE_LOG
SET log_value = '更新后的压力值'
WHERE log_id = 1;
```

---

##### 5.2.4 设备分组管理

设备分组表 `IOT_GROUP` 用于管理设备的分组，以下是一些常见的操作：

```sql
-- 插入设备分组
INSERT INTO IOT_GROUP (group_name, group_order, parent_id, del_flag, remark)
VALUES ('设备分组A', 1, NULL, '0', '第一组设备');

-- 将设备加入分组
INSERT INTO IOT_DEVICE_GROUP (device_id, group_id, remark)
VALUES (1, 1, '设备1加入设备分组A');

-- 查询设备分组中的设备
SELECT g.group_name, d.device_name
FROM IOT_GROUP g
JOIN IOT_DEVICE_GROUP dg ON g.group_id = dg.group_id
JOIN IOT_DEVICE d ON dg.device_id = d.device_id
WHERE g.group_id = 1;
```

---

##### 5.2.5 设备与用户的关联

```sql
-- 创建业主用户
INSERT INTO IOT_USER (user_name, del_flag, remark)
VALUES ('业主A', '0', '业主用户A');

-- 创建设备与用户的关联，假设设备ID为1，用户ID为1
INSERT INTO IOT_DEVICE_USER (user_id, device_id, is_owner, perms, remark)
VALUES (1, 1, 1, 'read,write', '业主A对设备1的读写权限');

-- 查询用户对设备的权限
SELECT u.user_name, d.device_name, du.perms
FROM IOT_USER u
JOIN IOT_DEVICE_USER du ON u.user_id = du.user_id
JOIN IOT_DEVICE d ON du.device_id = d.device_id
WHERE u.user_id = 1 AND d.device_id = 1;

-- 更新用户权限为只读
UPDATE IOT_DEVICE_USER
SET perms = 'read'
WHERE user_id = 1 AND device_id = 1;

-- 查询用户所有设备的信息
SELECT u.user_name, d.device_name
FROM IOT_USER u
JOIN IOT_DEVICE_USER du ON u.user_id = du.user_id
JOIN IOT_DEVICE d ON du.device_id = d.device_id
WHERE u.user_id = 1;

-- 删除用户对设备的权限 可以直接删除 `IOT_DEVICE_USER` 表中的相关记录。
DELETE FROM IOT_DEVICE_USER
WHERE user_id = 1 AND device_id = 1;

```