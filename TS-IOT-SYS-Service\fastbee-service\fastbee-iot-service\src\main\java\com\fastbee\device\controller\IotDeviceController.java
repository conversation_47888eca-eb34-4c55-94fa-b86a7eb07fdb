package com.fastbee.device.controller;

import java.io.IOException;
import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import com.fastbee.common.utils.MessageUtils;
import com.fastbee.common.utils.StringUtils;
import com.fastbee.device.domain.DeviceMonitor.IotGroup;
import com.fastbee.device.domain.IotDeviceImportVO;
import com.fastbee.device.domain.IotDeviceShortOutput;
import com.fastbee.device.domain.ThingsModel.IotDevicesInput;
import com.fastbee.device.mapper.DeviceMonitorMapper;
import com.fastbee.device.service.impl.IotDeviceGroupServiceImpl;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.fastbee.common.annotation.Log;
import com.fastbee.common.core.controller.BaseController;
import com.fastbee.common.core.domain.AjaxResult;
import com.fastbee.common.enums.BusinessType;
import com.fastbee.device.domain.IotDevice;
import com.fastbee.device.service.IIotDeviceService;
import com.fastbee.common.utils.poi.ExcelUtil;
import com.fastbee.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 设备Controller
 */
@RestController
@RequestMapping("/iot/iotDevice")
public class IotDeviceController extends BaseController
{
    @Autowired
    private IIotDeviceService iotDeviceService;

    @Autowired
    private IotDeviceGroupServiceImpl iotDeviceGroupServiceImpl;

    /**
     * 查询设备列表
     */
    @PreAuthorize("@ss.hasPermi('iot:iotDevice:list')")
    @GetMapping("/list")
    public TableDataInfo list(IotDevice iotDevice)
    {
        startPage();
        List<IotDevice> list = iotDeviceService.selectIotDeviceList(iotDevice);
        return getDataTable(list);
    }

    /**
     * 导出设备管理列表
     */
    @PreAuthorize("@ss.hasPermi('iot:iotDevice:export')")
    @Log(title = "设备管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IotDevice iotDevice)
    {
        List<IotDevice> list = iotDeviceService.selectIotDeviceList(iotDevice);
        ExcelUtil<IotDevice> util = new ExcelUtil<IotDevice>(IotDevice.class);
        util.exportExcel(response, list, "设备管理数据");
    }

    /**
     * 获取设备管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('iot:iotDevice:query')")
    @GetMapping(value = "/{deviceId}")
    @ApiOperation("获取设备详情")
    public AjaxResult getInfo(@PathVariable("deviceId") Long deviceId)
    {
        IotDevice device = iotDeviceService.selectIotDeviceByDeviceId(deviceId);
        return AjaxResult.success(device);
    }

    /**
     * 新增设备管理
     */
    @PreAuthorize("@ss.hasPermi('iot:iotDevice:add')")
    @Log(title = "设备管理", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("添加设备")
    public AjaxResult add(@RequestBody IotDevice iotDevice)
    {
        return AjaxResult.success(iotDeviceService.insertIotDevice(iotDevice));
    }

    /**
     * 修改设备管理
     */
    @PreAuthorize("@ss.hasPermi('iot:iotDevice:edit')")
    @Log(title = "设备管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改设备")
    public AjaxResult edit(@RequestBody IotDevice iotDevice)
    {
        return iotDeviceService.updateIotDevice(iotDevice);
    }

    /**
     * 删除设备管理
     */
    @PreAuthorize("@ss.hasPermi('iot:iotDevice:remove')")
    @Log(title = "设备管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{deviceId}")
    @ApiOperation("批量删除设备")
    public AjaxResult remove(@PathVariable Long deviceId)  throws SchedulerException
    {
        return toAjax(iotDeviceService.deleteIotDeviceByDeviceIds(deviceId));
    }


    /**
     * 生成设备编号
     */
    @PreAuthorize("@ss.hasPermi('iot:iotDevice:add')")
    @GetMapping("/generator")
    @ApiOperation("生成设备编号")
    public AjaxResult generatorDeviceNum(Integer type){
        return AjaxResult.success("操作成功",iotDeviceService.generationDeviceNum(type));
    }

    /**
     * 查询分组可添加设备
     */
    @PreAuthorize("@ss.hasPermi('iot:iotDevice:list')")
    @GetMapping("/listByGroup")
    @ApiOperation("查询分组可添加设备分页列表")
    public TableDataInfo listByGroup(IotDevice device)
    {
        startPage();
        return getDataTable(iotDeviceService.selectDeviceListByGroup(device));
    }

    /**
     * 查询设备简短列表，主页列表数据
     */
    @PreAuthorize("@ss.hasPermi('iot:iotDevice:list')")
    @GetMapping("/shortList")
    @ApiOperation("设备分页简短列表")
    public TableDataInfo shortList(IotDevice device)
    {
        Long groupId = device.getGroupId();
        if (groupId != null) {
            // 获取所有子组 ID
            Long[] groupIds = iotDeviceGroupServiceImpl.getAllSubGroupIds(groupId);

            // 创建可变列表并添加当前组 ID
            List<Long> allGroupIds = new ArrayList<>(Arrays.asList(groupIds));
            allGroupIds.add(groupId);
            // 设置查询参数
            device.setGroupIds(allGroupIds);
        }

        startPage();
        List<IotDeviceShortOutput> list = iotDeviceService.selectDeviceShortList(device);
        return getDataTable(list);
    }

    /**
     * 根据产品id查询物模型模板ID
     */
    @PreAuthorize("@ss.hasPermi('iot:iotDevice:list')")
    @ApiOperation("根据产品id查询物模型模板ID")
    @GetMapping("/getTemp")
    public AjaxResult getTemp(Long productId){
        //return AjaxResult.success(iotDeviceService.selectDeviceTemplateByProduct(productId));
        return AjaxResult.success();

    }

    /**
     * 获取设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('iot:iotDevice:query')")
    @GetMapping(value = "/runningStatus")
    @ApiOperation("获取设备详情和运行状态")
    public AjaxResult getRunningStatusInfo(Long deviceId)
    {
        return AjaxResult.success(iotDeviceService.selectDeviceRunningStatusByDeviceId(deviceId));
    }

    /**
     * 获取设备MQTT连接参数
     */
    @PreAuthorize("@ss.hasPermi('iot:iotDevice:query')")
    @GetMapping("/getMqttConnectData")
    @ApiOperation("获取设备MQTT连接参数")
    public AjaxResult getMqttConnectData(Long deviceId){
        return AjaxResult.success(iotDeviceService.getMqttConnectData(deviceId));
    }

    /**
     * 更新关联子设备
     */
    @PreAuthorize("@ss.hasPermi('iot:iotDevice:edit')")
    @Log(title = "更新关联子设备", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/updateDevices")
    @ApiOperation("更新关联子设备")
    public AjaxResult addSub(@RequestBody IotDevicesInput iotDevice)
    {
        return AjaxResult.success(iotDeviceService.updateDevices(iotDevice));
    }

    /**
     * 获取关联子设备ID数组
     */
    @PreAuthorize("@ss.hasPermi('iot:deviceGroup:query')")
    @GetMapping(value = "/getDeviceIds/{deviceId}")
    @ApiOperation("获取关联子设备ID数组")
    public AjaxResult getDeviceIds(@PathVariable("deviceId") Long deviceId)
    {
        return AjaxResult.success(iotDeviceService.getDeviceIds(deviceId));
    }

    /**
     * 获取设备物模型遥测属性值
     */
    @PreAuthorize("@ss.hasPermi('iot:iotDevice:query')")
    @GetMapping(value = "/thingsModelList/{deviceId}")
    @ApiOperation("获取设备物模型遥测属性值")
    public AjaxResult getThingsModelList(@PathVariable("deviceId") Long deviceId)
    {
        IotDeviceShortOutput device = iotDeviceService.selectThingsModelList(deviceId);
        return AjaxResult.success(device);
    }

    /**
     * 根据设备编号详细信息
     */
    @PreAuthorize("@ss.hasPermi('iot:iotDevice:query')")
    @GetMapping(value = "/getDeviceBySerialNumber/{serialNumber}")
    @ApiOperation("根据设备编号获取设备详情")
    public AjaxResult getInfoBySerialNumber(@PathVariable("serialNumber") String serialNumber)
    {
        return AjaxResult.success(iotDeviceService.selectDeviceBySerialNumber(serialNumber));
    }

    //默认所属单位名字
    @Value("${default.groupName:海南三区采油厂}")
    private String groupName;

    @Autowired
    private DeviceMonitorMapper deviceMonitorMapper;

    @PreAuthorize("@ss.hasPermi('iot:device:add')")
    @ApiOperation("下载设备导入模板")
    @PostMapping("/uploadTemplate")
    public void uploadTemplate(HttpServletResponse response, @RequestParam(name = "type") Integer type)
    {
        try {
            //所属单位
            List<String> groupNames = deviceMonitorMapper.findGroupTree(groupName).stream()
                    .filter(group -> group.getLevel() == 1)
                    .map(IotGroup::getGroupName)
                    .collect(Collectors.toList());
            // 构建模板数据列表，每行一个分组名
            List<IotDeviceImportVO> templateList = new ArrayList<>();
            for (String groupName : groupNames) {
                IotDeviceImportVO vo = new IotDeviceImportVO();
                vo.setAllGroupNames(groupName);
                templateList.add(vo);
            }
        // 1-设备导入；
//        if (1 == type) {
            ExcelUtil<IotDeviceImportVO> util = new ExcelUtil<>(IotDeviceImportVO.class);
            util.exportExcel(response,templateList, "设备导入");
//        }
            } catch (Exception e) {
            // 可以记录日志或返回错误信息
            e.printStackTrace();
            try {
                response.getWriter().write("导入模板下载失败：" + e.getMessage());
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
    }

    @PreAuthorize("@ss.hasPermi('iot:device:add')")
    @ApiOperation("批量导入设备")
    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(@RequestParam("file") MultipartFile file, @RequestParam("productId") Long productId) throws Exception
    {
        if (null == file) {
            return error(MessageUtils.message("import.failed.file.null"));
        }
        ExcelUtil<IotDeviceImportVO> util = new ExcelUtil<>(IotDeviceImportVO.class);
        List<IotDeviceImportVO> deviceImportVOList = util.importExcel(file.getInputStream());
        if (CollectionUtils.isEmpty(deviceImportVOList)) {
            return error(MessageUtils.message("import.failed.data.null"));
        }
        IotDeviceImportVO deviceImportVO = deviceImportVOList.stream().filter(d -> StringUtils.isEmpty(d.getDeviceName())).findAny().orElse(null);
        if (null != deviceImportVO) {
            return error(MessageUtils.message("import.failed.device.name.null"));
        }
        String message = iotDeviceService.importDevice(deviceImportVOList, productId);
        return StringUtils.isEmpty(message) ? success(MessageUtils.message("import.success")) : error(message);
    }
}
