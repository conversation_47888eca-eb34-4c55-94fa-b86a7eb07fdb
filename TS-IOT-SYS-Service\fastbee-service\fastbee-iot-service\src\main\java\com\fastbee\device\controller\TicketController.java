package com.fastbee.device.controller;

import com.fastbee.common.annotation.Log;
import com.fastbee.common.core.controller.BaseController;
import com.fastbee.common.core.domain.AjaxResult;
import com.fastbee.common.core.page.TableDataInfo;
import com.fastbee.common.enums.BusinessType;
import com.fastbee.iot.domain.Ticket;
import com.fastbee.iot.service.ITicketService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "工单模块")
@RestController
@RequestMapping("/iot/ticket")
public class TicketController extends BaseController
{
    @Autowired
    private ITicketService ticketService;

    /**
     * 查询工单列表
     */
    @ApiOperation("查询工单列表")
    @PreAuthorize("@ss.hasPermi('iot:ticket:list')")
    @GetMapping("/list")
    public TableDataInfo list(Ticket ticket)
    {
        startPage();
        List<Ticket> list = ticketService.selectTicketList(ticket);
        return getDataTable(list);
    }

    /**
     * 获取工单详细信息
     */
    @ApiOperation("获取工单详细信息")
    @PreAuthorize("@ss.hasPermi('iot:ticket:query')")
    @GetMapping(value = "/{ticketId}")
    public AjaxResult getInfo(@PathVariable("ticketId") Long ticketId)
    {
        return AjaxResult.success(ticketService.selectTicketByTicketId(ticketId));
    }

    /**
     * 新增工单
     */
    @ApiOperation("新增工单")
    @PreAuthorize("@ss.hasPermi('iot:ticket:add')")
    @Log(title = "新增工单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Ticket ticket)
    {
        return toAjax(ticketService.insertTicket(ticket));
    }

    /**
     * 修改工单
     */
    @ApiOperation("修改工单")
    @PreAuthorize("@ss.hasPermi('iot:ticket:edit')")
    @Log(title = "修改工单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Ticket ticket)
    {
        return toAjax(ticketService.updateTicket(ticket));
    }

    /**
     * 删除工单
     */
    @ApiOperation("删除工单")
    @PreAuthorize("@ss.hasPermi('iot:ticket:remove')")
    @Log(title = "删除工单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ticketIds}")
    public AjaxResult remove(@PathVariable Long[] ticketIds)
    {
        return toAjax(ticketService.deleteTicketByTicketIds(ticketIds));
    }

}
