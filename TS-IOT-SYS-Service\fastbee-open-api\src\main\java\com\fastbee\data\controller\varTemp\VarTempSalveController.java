package com.fastbee.data.controller.varTemp;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.fastbee.common.annotation.Log;
import com.fastbee.common.core.controller.BaseController;
import com.fastbee.common.core.domain.AjaxResult;
import com.fastbee.common.enums.BusinessType;
import com.fastbee.iot.domain.VarTempSalve;
import com.fastbee.iot.service.IVarTempSalveService;
import com.fastbee.common.utils.poi.ExcelUtil;
import com.fastbee.common.core.page.TableDataInfo;

/**
 * 变量模板设备从机Controller
 * 
 * <AUTHOR>
 * @date 2022-11-30
 */
@Api(tags = "变量模板设备从机")
@RestController
@RequestMapping("/iot/salve")
public class VarTempSalveController extends BaseController
{
    @Autowired
    private IVarTempSalveService varTempSalveService;

    /**
     * 查询变量模板设备从机列表
     */
    @ApiOperation("查询变量模板设备从机列表")
    @PreAuthorize("@ss.hasPermi('iot:salve:list')")
    @GetMapping("/list")
    public TableDataInfo list(VarTempSalve varTempSalve)
    {
        startPage();
        List<VarTempSalve> list = varTempSalveService.selectVarTempSalveList(varTempSalve);
        return getDataTable(list);
    }

    /**
     * 导出变量模板设备从机列表
     */
    @ApiOperation("导出变量模板设备从机列表")
    @PreAuthorize("@ss.hasPermi('iot:salve:export')")
    @Log(title = "变量模板设备从机", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VarTempSalve varTempSalve)
    {
        List<VarTempSalve> list = varTempSalveService.selectVarTempSalveList(varTempSalve);
        ExcelUtil<VarTempSalve> util = new ExcelUtil<VarTempSalve>(VarTempSalve.class);
        util.exportExcel(response, list, "变量模板设备从机数据");
    }

    /**
     * 获取变量模板设备从机详细信息
     */
    @ApiOperation("获取变量模板设备从机详细信息")
    @PreAuthorize("@ss.hasPermi('iot:salve:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(varTempSalveService.selectVarTempSalveById(id));
    }

    /**
     * 新增变量模板设备从机
     */
    @ApiOperation("新增变量模板设备从机")
    @PreAuthorize("@ss.hasPermi('iot:salve:add')")
    @Log(title = "变量模板设备从机", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VarTempSalve varTempSalve)
    {
        return toAjax(varTempSalveService.insertVarTempSalve(varTempSalve));
    }

    /**
     * 修改变量模板设备从机
     */
    @ApiOperation("修改变量模板设备从机")
    @PreAuthorize("@ss.hasPermi('iot:salve:edit')")
    @Log(title = "变量模板设备从机", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VarTempSalve varTempSalve)
    {
        return toAjax(varTempSalveService.updateVarTempSalve(varTempSalve));
    }

    /**
     * 删除变量模板设备从机
     */
    @ApiOperation("删除变量模板设备从机")
    @PreAuthorize("@ss.hasPermi('iot:salve:remove')")
    @Log(title = "变量模板设备从机", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(varTempSalveService.deleteVarTempSalveByIds(ids));
    }

    /**
     * 根据产品id查询从机列表
     */
    @ApiOperation("根据产品id查询从机列表")
    @PreAuthorize("@ss.hasPermi('iot:salve:query')")
    @GetMapping("/listByPId")
    public AjaxResult listByPId(Long productId){
        return AjaxResult.success(varTempSalveService.selectVarTempSalveListByProductId(productId));
    }
}
