package com.fastbee.device.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fastbee.common.core.redis.RedisCache;
import com.fastbee.common.core.redis.RedisKeyBuilder;
import com.fastbee.common.exception.ServiceException;
import com.fastbee.device.domain.DeviceMonitor.*;
import com.fastbee.device.mapper.DeviceMonitorMapper;
import com.fastbee.device.service.IDeviceMonitorService;
import com.fastbee.iot.domain.Scene;
import com.fastbee.iot.service.impl.SceneServiceImpl;
import com.fastbee.iot.tdengine.service.ILogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;


/**
 * 设备管理Service业务层处理
 */
@Service
public class DeviceMonitorServiceImpl implements IDeviceMonitorService
{
    // 定义日期时间格式化器为静态常量，避免重复创建
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    //默认所属单位名字
    @Value("${default.groupName:海南三区采油厂}")
    private String groupName;

    @Autowired
    private DeviceMonitorMapper deviceMonitorMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ILogService logService;

    /**
     * 查询设备监测范围
     */
    @Override
    public List<JSONObject> scope()
    {
        List<JSONObject> resultList = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();

        //所属单位
        Long[] groupIds = getCurrentGroupIds(groupName);

        //查询所有设备以及不同状态的设备数
        //★★★★★★★★★★★★根据物模型状态属性查询，只有仪表类有设备状态★★★★★★★★★★★★★★
        DeviceStatusCount deviceStatusCount = deviceMonitorMapper.selectDeviceStatusCount(groupIds);

        //更新设备在装总数到 Redis 缓存中
        updateDeviceCountToCache(deviceStatusCount);

        jsonObject.put("deviceStatusCount",deviceStatusCount);
//        //查询仪表类下的所有产品
//        List<IotProduct> productList = deviceMonitorMapper.selectIotProductLists();
//
//        //查询产品下设备监控（在装）数
//        for(IotProduct product:productList){
//            ProductDeviceCount deviceCount = new ProductDeviceCount();
//            deviceCount.setProductId(product.getProductId());
//            deviceCount.setProductName(product.getProductName());
//        }
//
//        //查询所有产品下设备数量
//        List<ProductDeviceCount> productDeviceCounts = deviceMonitorMapper.selectDeviceCountByProductId();

        //查询仪表类产品下设备监控（在装）数
        List<ProductDeviceCount> productDeviceCounts = deviceMonitorMapper.selectDeviceCounts(groupIds);

        jsonObject.put("productDeviceCounts",productDeviceCounts);
        resultList.add(jsonObject);
        return resultList;
    }

    //所属单位
    public Long[] getCurrentGroupIds(String groupName) {
        List<IotGroup> groupList = deviceMonitorMapper.findGroupTree(groupName);
        if (groupList == null || groupList.isEmpty()) {
            return new Long[0];
        }
        return groupList.stream()
                .map(IotGroup::getGroupId)
                .toArray(Long[]::new);
    }

    /**
     * 更新设备在装总数到 Redis 缓存中
     */
    public void updateDeviceCountToCache(DeviceStatusCount deviceStatusCount) {
        // 获取当前日期（格式化为 MM-dd）
        String date = getCurrentDateFormatted("MM-dd");

        // 构建缓存键
        String cacheKey = RedisKeyBuilder.deviceCountKey(date);

        // 获取现有的 Map 或初始化一个新的 Map
        Map<String, String> maps = redisCache.hashEntity(cacheKey);
        if (CollectionUtils.isEmpty(maps)) {
            maps = new HashMap<>();
        }

        // 向 Map 中添加或更新设备在装总数和日期
        maps.put("totalCount", deviceStatusCount.getInstalledCount()==null?"0":deviceStatusCount.getInstalledCount().toString());
        maps.put("date", date);

        // 将更新后的 Map 放回 Redis 缓存
        redisCache.hashPutAll(cacheKey, maps);
    }

    /**
     * 获取当前日期并格式化为指定格式的字符串
     * @param pattern 日期格式（如 "MM-dd", "yyyy-MM-dd" 等）
     * @return 格式化后的日期字符串
     */
    public String getCurrentDateFormatted(String pattern) {
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return today.format(formatter);
    }

    /**
     * 获取当前日期及过去一周的日期列表
     * @param pattern 日期格式（如 "MM-dd", "yyyy-MM-dd" 等）
     * @return 包含过去一周日期的字符串列表
     */
    public List<String> getPastWeekDatesFormatted(String pattern) {

        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);

        // 存储过去一周日期的列表
        List<String> pastWeekDates = new ArrayList<>();

        // 遍历过去 7 天（包括今天）
        for (int i = 0; i < 7; i++) {
            // 计算过去的日期
            LocalDate pastDate = today.minusDays(i);

            // 格式化日期并添加到列表中
            pastWeekDates.add(pastDate.format(formatter));
        }

        // 返回结果列表
        return pastWeekDates;
    }

    /**
     * 查询设备监测动态
     */
    @Override
    public List<Map<String, String>> dynamics()
    {
        List<Map<String, String>> resultList = new ArrayList<>();

        // 获取过去一周的日期列表（格式为 MM-dd）
        List<String> pastWeekDates = getPastWeekDatesFormatted("MM-dd");
        for (String date : pastWeekDates) {
            Map<String, String> map = updateTodayDeviceData(date);
            // 将处理后的 Map 添加到结果列表
            resultList.add(map);
        }

        // 按日期升序排序
        resultList.sort((map1, map2) -> {
            String date1 = map1.get("date");
            String date2 = map2.get("date");
            return date1.compareTo(date2);
        });

        return resultList;
    }

    /**
     * 更新今天的设备统计数据并存储到 Redis 缓存中
     */
    private Map<String, String> updateTodayDeviceData(String date) {
        String cacheKey = RedisKeyBuilder.deviceCountKey(date);
        // 从 Redis 缓存中获取 Map 数据
        Map<String, String> map = redisCache.hashEntity(cacheKey);
        if (map == null) {
            map = new HashMap<>();
        }

        //所属单位
        Long[] groupIds = getCurrentGroupIds(groupName);

        // 获取设备总数（优先从缓存取，否则查库）
        int totalCount = getTotalCountFromCacheOrDB(map,groupIds);

        map.put("totalCount", String.valueOf(totalCount));
        map.put("date", date);

        // 如果当前日期是今天，更新缓存数据
        String now = getCurrentDateFormatted("MM-dd");
        if(now.equals(date)){
            //在线设备数：根据groupId查询所有设备信息/
            List<IotDeviceInfo> iotDeviceInfos = deviceMonitorMapper.selectDeviceInfoByGroupId(groupIds);
            int onlineCount = countOnlineDevices(iotDeviceInfos);

            //在线设备数：(过去一个小时可以读取属性的设备数)
//        int onlineCount = logService.selectOnlineCount();
            map.put("onlineCount", String.valueOf(onlineCount));

            // 异常设备数
            int abnormalCount = totalCount - onlineCount;
            map.put("abnormalCount", String.valueOf(abnormalCount));

            // 采集齐全率
            double collectionRate = totalCount == 0 ? 0.0 : ((double) onlineCount / totalCount) * 100;
            map.put("collectionRate", String.format("%.2f%%", collectionRate));
        }else{
            map.putIfAbsent("onlineCount", "0");
            map.putIfAbsent("abnormalCount", String.valueOf(totalCount));
            map.putIfAbsent("collectionRate", "0.00%");
        }
        // 更新缓存
        redisCache.hashPutAll(cacheKey, map);
        return map;
    }

    // 封装获取设备总数 totalCount
    private int getTotalCountFromCacheOrDB(Map<String, String> map , Long[] groupIds) {
        if (map.containsKey("totalCount")) {
            try {
                return Integer.parseInt(map.get("totalCount"));
            } catch (NumberFormatException e) {
                System.err.println("无法将 totalCount 转换为整数: " + map.get("totalCount"));
            }
        }

        DeviceStatusCount deviceStatusCount = deviceMonitorMapper.selectDeviceStatusCount(groupIds);
        int totalCount = deviceStatusCount != null ? deviceStatusCount.getInstalledCount() : 0;
        map.put("totalCount", String.valueOf(totalCount));
        return totalCount;
    }

    // 统计在线设备数（status == 3）
    private int countOnlineDevices(List<IotDeviceInfo> devices) {
        return (int) devices.stream()
                .filter(device -> device.getStatus() == 3)
                .count();
    }

    /**
     * 查询采集齐全率分析
     */
    @Override
    public List<Map<String, String>> completenessRate()
    {
        List<Map<String, String>> resultList = new ArrayList<>();

        //所属单位
        List<IotGroup> findGroupTree = deviceMonitorMapper.findGroupTree(groupName);
        for(IotGroup groupTree : findGroupTree){
            if(groupTree.getLevel()==1){
                Map<String, String> map = new HashMap<>();
                Long groupId = groupTree.getGroupId();
                map.put("groupName", groupTree.getGroupName());

                //设备总数：根据groupId查询仪表类产品下设备监控（在装）数
                int totalCount = deviceMonitorMapper.selectDeviceCountByGroupId(groupId);
                map.put("totalCount", String.valueOf(totalCount));

                //在线设备数：根据groupId查询所有设备信息/(过去一个小时可以读取属性的设备数)
                Long[] groupIds = {groupId};
                List<IotDeviceInfo> iotDeviceInfos = deviceMonitorMapper.selectDeviceInfoByGroupId(groupIds);
                int onlineCount = countOnlineDevices(iotDeviceInfos);

                //根据设备编号查询（过去一个小时）设备是否读取属性值
//                int onlineCount = (int) iotDeviceInfos.stream()
//                        .filter(device -> logService.selectOnlineCountBySerialNumber(device.getSerialNumber()) > 0)
//                        .count();
                map.put("onlineCount", String.valueOf(onlineCount));

                // 异常设备数
                int abnormalCount = totalCount - onlineCount;
                map.put("abnormalCount", String.valueOf(abnormalCount));

                // 采集齐全率
                double collectionRate = totalCount == 0 ? 0.0 : ((double) onlineCount / totalCount) * 100;
                map.put("collectionRate", String.format("%.2f%%", collectionRate));

                // 将处理后的 Map 添加到结果列表
                resultList.add(map);
            }
        }

        return resultList;
    }


    /**
     * 查询设备故障分类对比
     */
    @Override
    public Map<String, Object> classification()
    {
        //时间范围★★★★★★★  (默认一周)  ★★★★★★★
        LocalDateTime now = LocalDateTime.now();
        // 开始时间：7天前
        String beginTime = now.minusDays(7).format(DATE_TIME_FORMATTER);
        String endTime = now.format(DATE_TIME_FORMATTER);

        //所属单位
        Long[] groupIds = getCurrentGroupIds(groupName);

        //查询仪表类产品下设备监控（在装）数
        List<ProductDeviceCount> productDeviceCounts = deviceMonitorMapper.selectDeviceCounts(groupIds);

        // 初始化结果列表和总故障数
        List<FaultDeviceCount> faultDeviceCounts = new ArrayList<>();
        int totalCount = 0;

        for(ProductDeviceCount product : productDeviceCounts){
            FaultDeviceCount result = buildFaultDeviceCount(groupIds, product, beginTime, endTime);
            // 累加总数
            totalCount += result.getDeviceFaultsCount();
            faultDeviceCounts.add(result);
        }
        // 设置总数到静态字段
//        FaultDeviceCount.setTotalCount(totalCount);

        // 计算每个产品的故障百分比
        for (FaultDeviceCount item : faultDeviceCounts) {
            item.setFaultPercentage(calculatePercentage(item.getDeviceFaultsCount(), totalCount));
        }

        Map<String, Object> result = new HashMap<>();
        result.put("faultDeviceCounts", faultDeviceCounts);
        result.put("totalCount", totalCount);
        return result;
    }

    // 计算百分比（保留两位小数）
    private double calculatePercentage(int part, int total) {
        if (total <= 0) return 0.0;
        return Math.round(((double) part / total) * 10000) / 100.0;
    }

    // 构建单个 FaultDeviceCount 对象
    private FaultDeviceCount buildFaultDeviceCount(Long[] groupIds, ProductDeviceCount product,
                                                   String beginTime, String endTime) {
        FaultDeviceCount faultDeviceCount = new FaultDeviceCount();
        //分组id
        Long categoryId = product.getProductId();
        //根据分组id查询产品id
        List<Long> productIds = deviceMonitorMapper.selectProductId(categoryId);
        int deviceFaultsCount = 0;
        for (Long productId:productIds){
            // 故障设备数量
            List<String> iotDeviceSN = deviceMonitorMapper.selectDeviceInfoByProductId(groupIds, productId);
            List<String> faultsSN = deviceMonitorMapper.selectDeviceFaultsCount(groupIds, productId, beginTime, endTime);
            List<String> allFaultSN = mergeAndDeduplicateSN(iotDeviceSN, faultsSN);
            int deviceFaultsCount1 = allFaultSN == null ? 0 : allFaultSN.size();
            deviceFaultsCount = deviceFaultsCount + deviceFaultsCount1;
        }
        faultDeviceCount.setProductId(categoryId);
        faultDeviceCount.setProductName(product.getProductName());
        faultDeviceCount.setDeviceFaultsCount(deviceFaultsCount);

        return faultDeviceCount;
    }

    /**
     * 查询设备故障分类对比
     */
    @Override
    public Map<String, Object> weeklyComparison()
    {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 本周（周一到周日）
        LocalDateTime startOfThisWeek = now.with(DayOfWeek.MONDAY);
        LocalDateTime endOfThisWeek = now.with(DayOfWeek.SUNDAY);
        String thisWeekStart = startOfThisWeek.format(DATE_TIME_FORMATTER);
        String thisWeekEnd = endOfThisWeek.format(DATE_TIME_FORMATTER);

        // 上周（上周周一到上周周日）
        LocalDateTime startOfLastWeek = startOfThisWeek.minusWeeks(1);
        LocalDateTime endOfLastWeek = endOfThisWeek.minusWeeks(1);
        String lastWeekStart = startOfLastWeek.format(DATE_TIME_FORMATTER);
        String lastWeekEnd = endOfLastWeek.format(DATE_TIME_FORMATTER);

        //所属单位
        Long[] groupIds = getCurrentGroupIds(groupName);
        //查询仪表类产品下设备监控（在装）数
        List<ProductDeviceCount> productDeviceCounts = deviceMonitorMapper.selectDeviceCounts(null);

        // 初始化本周和上周的故障总数
        int thisTotal = 0, lastTotal = 0;
        List<FaultDeviceCount> thisWeekList = new ArrayList<>();
        List<FaultDeviceCount> lastWeekList = new ArrayList<>();

        for(ProductDeviceCount product : productDeviceCounts){
            // 查询本周的故障数量
            FaultDeviceCount thisWeek = buildWeeklyFaultCount(groupIds, product, thisWeekStart, thisWeekEnd);
            // 查询上周的故障数量
            FaultDeviceCount lastWeek = buildWeeklyFaultCount(groupIds, product, lastWeekStart, lastWeekEnd);

            thisTotal += thisWeek.getDeviceFaultsCount();
            lastTotal += lastWeek.getDeviceFaultsCount();

            thisWeekList.add(thisWeek);
            lastWeekList.add(lastWeek);
        }
        // 设置本周和上周的总故障数到静态字段
//        FaultDeviceCount.setThisWeekTotalCount(thisWeekTotalFaults);
//        FaultDeviceCount.setLastWeekTotalCount(lastWeekTotalFaults);
        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("thisWeekDeviceCounts", thisWeekList);
        result.put("lastWeekDeviceCounts", lastWeekList);
        result.put("thisWeekTotalFaults", thisTotal);
        result.put("lastWeekTotalFaults", lastTotal);
        return result;
    }

    // 构建每周故障数量
    private FaultDeviceCount buildWeeklyFaultCount(Long[] groupIds, ProductDeviceCount product,
                                                   String beginTime, String endTime) {
        FaultDeviceCount count = new FaultDeviceCount();
        //分组id
        Long categoryId = product.getProductId();
        //根据分组id查询产品id
        List<Long> productIds = deviceMonitorMapper.selectProductId(categoryId);
        int deviceFaultsCount = 0;
        for (Long productId:productIds) {
            List<String> faultsSN = deviceMonitorMapper.selectDeviceFaultsCount(groupIds, productId, beginTime, endTime);
            int deviceFaultsCount1 = faultsSN == null ? 0 : faultsSN.size();
            deviceFaultsCount = deviceFaultsCount + deviceFaultsCount1;
        }
        count.setProductId(categoryId);
        count.setProductName(product.getProductName());
        count.setDeviceFaultsCount(deviceFaultsCount);
        return count;
    }


    // 合并两个 SN 列表并去重
    public static List<String> mergeAndDeduplicateSN(List<String> list1, List<String> list2) {
        if (list1 == null) list1 = new ArrayList<>();
        if (list2 == null) list2 = new ArrayList<>();

        return Stream.concat(list1.stream(), list2.stream())
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 查询设备运行统计
     */
    @Override
    public List<DeviceOperationStats> operationStats(IotGroup iotGroup)
    {
        String groupName = iotGroup.getGroupName();
        if(groupName == null || groupName.trim().isEmpty()){
            throw new ServiceException("未传单位信息");
        }

        //时间范围★★★★★★★  (默认一周)  ★★★★★★★
        LocalDateTime now = LocalDateTime.now();
        // 开始时间：7天前
        String beginTime = now.minusDays(7).format(DATE_TIME_FORMATTER);
        String endTime = now.format(DATE_TIME_FORMATTER);

        //所属单位
        Long[] groupIds = getCurrentGroupIds(groupName);
        //查询仪表类产品下设备监控（在装）数
        List<ProductDeviceCount> productDeviceCounts = deviceMonitorMapper.selectDeviceCounts(groupIds);

        List<DeviceOperationStats> resultList = new ArrayList<>();

        for(ProductDeviceCount product : productDeviceCounts){
            DeviceOperationStats deviceOperationStats = new DeviceOperationStats();

            //分组id
            Long categoryId = product.getProductId();
            //根据分组id查询产品id
            List<Long> productIds = deviceMonitorMapper.selectProductId(categoryId);
            int deviceFaultsCount = 0;
            //仪表设备总数
            int deviceCount = 0;
            for (Long productId:productIds) {
                // 不在线设备编号
                List<String> iotDeviceSN = deviceMonitorMapper.selectDeviceInfoByProductId(groupIds, productId);
                // 如果设备数量为 0，则故障数量为 0；否则查询故障设备数量
                List<String> faultsSN = deviceMonitorMapper.selectDeviceFaultsCount(groupIds,productId, beginTime, endTime);
                //合并两个 SN 列表并去重
                List<String> allFaultSN = mergeAndDeduplicateSN(iotDeviceSN, faultsSN);
                int deviceFaultsCount1 = allFaultSN == null ? 0 : allFaultSN.size();
                deviceFaultsCount = deviceFaultsCount + deviceFaultsCount1;
                int count = deviceMonitorMapper.selectCountByProductId(groupIds, productId);
                deviceCount = deviceCount + count;
            }
            deviceOperationStats.setProductId(categoryId);
            deviceOperationStats.setProductName(product.getProductName());
            deviceOperationStats.setTotalCount(deviceCount);
            deviceOperationStats.setDeviceFaultsCount(deviceFaultsCount);
            //正常
            int deviceNormalCount = deviceCount - deviceFaultsCount;
            deviceOperationStats.setDeviceNormalCount(deviceNormalCount);
            resultList.add(deviceOperationStats);
        }

        return resultList;
    }

    @Resource
    private SceneServiceImpl sceneService;

    /**
     * 查询设备故障清单
     */
    @Override
    public List<DeviceFaultList> faultsList()
    {
        List<DeviceFaultList> resultList = deviceMonitorMapper.selectDeviceFaultsList();
        if (resultList.isEmpty()) {
            return Collections.emptyList();
        }

        // 批量缓存数据，避免在循环中反复查询数据库
        Map<String, String> groupMap = new HashMap<>();
        Map<String, String> deviceNameMap = new HashMap<>();

        for(DeviceFaultList device : resultList){
            String serialNumber = device.getSerialNumber();
            // 查询所属单位（仅首次查询）(根据设备编号查询设备分组名)
            if (!groupMap.containsKey(serialNumber)) {
                List<DeviceFaultList> list = deviceMonitorMapper.selectGroupNameBySN(serialNumber);
                groupMap.put(serialNumber, !list.isEmpty() ? list.get(0).getGroupName() : "未知");
            }
            device.setGroupName(groupMap.get(serialNumber));

            //查询所属名（仅首次查询）(根据设备编号查询从属设备名)
            if (!deviceNameMap.containsKey(serialNumber)) {
                String name = deviceMonitorMapper.selectBelongsDeviceNameBySN(serialNumber);
                deviceNameMap.put(serialNumber, name != null ? name : "未知");
            }
            device.setBelongsDeviceName(deviceNameMap.get(serialNumber));

            //故障原因faultReason(detail[{"id": "current_flow", "name": "当前流量", "value": "91"}, {"id": "pressure", "name": "压力值", "value": "130"}])
            //设备故障异常值：当前流量:异常值:91;压力值:异常值:130
            device.setFaultReason(parseDetailToReason(device.getDetail()));

            Scene scene=sceneService.selectSceneBySceneId(device.getSceneId());
            device.setScene(scene);
        }
        return resultList;
    }

    public String parseDetailToReason(String detail) {
        if (detail == null || detail.trim().isEmpty()) {
            return "无故障描述";
        }
        try {
            JSONArray jsonArray = JSON.parseArray(detail);
            // 构建结果字符串
            StringBuilder result = new StringBuilder("设备故障异常值:");
            for (int i = 0; i < jsonArray.size(); i++) {
                // 遍历 JSON 数组
                JSONObject obj = jsonArray.getJSONObject(i);
                String name = obj.getString("name");
                String value = obj.getString("value");
                // 拼接格式：属性名称:异常值:值
                result.append(name).append(":异常值:").append(value);
                // 如果不是最后一个元素，添加分号
                if (i < jsonArray.size() - 1) {
                    result.append(";");
                }
            }
            return result.toString();
        } catch (Exception e) {
            System.err.println("解析 detail 时发生错误：" + e.getMessage());
            return "";
        }
    }

    /**
     * 查询设备运维考核
     */
    @Override
    public List<DeviceFaultStatistics> maintenanceEvaluation()
    {
        // 获取当前年月
        int currentYear = LocalDate.now().getYear();
        int currentMonth = LocalDate.now().getMonthValue();
        Long[] groupIds = getCurrentGroupIds(groupName);

        // 使用 Stream API 生成每个月的统计数据
        return IntStream.rangeClosed(1, 12)
            .mapToObj(month -> {
                DeviceFaultStatistics stats = new DeviceFaultStatistics();
                stats.setDate(month);
                //2025-1
                String cacheKey = RedisKeyBuilder.deviceFaultsCountKey(currentYear + "-" + month);
                //尝试从缓存中获取数据
                Map<String, String> cachedData = redisCache.hashEntity(cacheKey);
                if (cachedData == null) {
                    cachedData = new HashMap<>();
                }

                // 未来月份：直接返回 0
                if (month > currentMonth) {
                    stats.setDeviceFaultsCount(0);
                    stats.setHandledFaultsCount(0);
                }else if(month < currentMonth){
                    if (cachedData != null && !cachedData.isEmpty()) {
                        // 缓存命中：设置数据
                        stats.setDeviceFaultsCount(Integer.parseInt(cachedData.getOrDefault("deviceFaultsCount", "0")));
                        stats.setHandledFaultsCount(Integer.parseInt(cachedData.getOrDefault("handledFaultsCount", "0")));
                    }else{
                        stats.setDeviceFaultsCount(0);
                        stats.setHandledFaultsCount(0);
                    }
                }else if (month == currentMonth) {
                    // 当前月份：计算从本月第一天到现在的真实数据
                    LocalDate startOfMonth = LocalDate.of(currentYear, month, 1);
                    LocalDateTime beginDateTime = startOfMonth.atStartOfDay();
                    LocalDateTime endDateTime = LocalDateTime.now();

                    String beginTime = beginDateTime.format(DATE_TIME_FORMATTER);
                    String endTime = endDateTime.format(DATE_TIME_FORMATTER);

                    //设备故障数
                    List<String> faultsSN = deviceMonitorMapper.selectDeviceFaultsCount(groupIds,null, beginTime, endTime);
                    int deviceFaultsCount = faultsSN == null ? 0 : faultsSN.size();

                    // TODO 后续补充“故障处置数”的实现
                    int handledFaultsCount = 0;
                    for(String faultSN : faultsSN){
                        //根据设备编号查询（过去一个小时）设备是否读取属性值
                        int onlineCount = logService.selectOnlineCountBySerialNumber(faultSN);
                        int faultsCount = deviceMonitorMapper.selectFaultsCountBySN(faultSN);
                        if(onlineCount > 0 && faultsCount == 0){
                            handledFaultsCount++;
                        }
                    }
                    stats.setDeviceFaultsCount(deviceFaultsCount);
                    stats.setHandledFaultsCount(handledFaultsCount);
                }
                // 更新缓存
                Map<String, String> dataToCache = new HashMap<>();
                dataToCache.put("deviceFaultsCount", String.valueOf(stats.getDeviceFaultsCount()));
                dataToCache.put("handledFaultsCount", String.valueOf(stats.getHandledFaultsCount()));
                redisCache.hashPutAll(cacheKey, dataToCache);
                return stats;
            })
                .collect(Collectors.toList());
    }

    /**
     * 查询设备故障区域排名
     */
    @Override
    public List<Map<String, String>> areaRanking()
    {
        List<Map<String, String>> resultList = new ArrayList<>();

        //时间范围★★★★★★★  (默认一周)  ★★★★★★★
        LocalDateTime now = LocalDateTime.now();
        // 开始时间：7天前
        String beginTime = now.minusDays(7).format(DATE_TIME_FORMATTER);
        String endTime = now.format(DATE_TIME_FORMATTER);

        //所属单位
        List<IotGroup> groupTreeList = deviceMonitorMapper.findGroupTree(groupName);
        return groupTreeList.stream()
                .filter(group -> group.getLevel() == 1)
                .map(group -> {
                    Long groupId = group.getGroupId();
                    String groupName = group.getGroupName();

                    int count = deviceMonitorMapper.selectCountByGroupId(groupId, beginTime, endTime);

                    Map<String, String> map = new HashMap<>();
                    map.put("groupName", groupName);
                    map.put("count", String.valueOf(count));
                    return map;
                })
                .collect(Collectors.toList());
    }

    /**
     * 查询所属单位列表
     */
    @Override
    public List<IotGroup> findGroupTree()
    {
        return deviceMonitorMapper.findGroupTree(groupName);
    }

}

